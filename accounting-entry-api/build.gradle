apply plugin: 'war'

def webContext = "br-accounting-entry"

configurations {
    all {
        resolutionStrategy {
            force 'com.rabbitmq:amqp-client:5.20.0'
        }
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    }
}

dependencies {
    implementation (project(':accounting-entry-domain')) {
        exclude group: "com.tr.bluemoon.brtap", module: "commons-kafka"
        exclude group: "io.cloudevents", module: "cloudevents-kafka"
        exclude group: "org.springframework.kafka", module: "spring-kafka"
        exclude group: "org.apache.kafka", module: "kafka-clients"
    }

    // Servlet API
    providedCompile 'org.apache.tomcat:tomcat-servlet-api:7.0.50'
    runtimeOnly 'org.glassfish.jaxb:jaxb-runtime:2.3.+'

    // OAuth
    // Exclude jackson and provide bumped version to solve Snyk vulnerabilities of this transitive dependency
    implementation ('com.auth0:auth0:2.12.0') {
        exclude group: 'com.fasterxml.jackson.core', module: 'jackson-core'
        exclude group: 'com.fasterxml.jackson.core', module: 'jackson-databind'
    }
    implementation 'com.auth0:java-jwt:3.10.1'
    implementation 'com.auth0:jwks-rsa:0.14.0'

    // Redis
    implementation 'io.lettuce:lettuce-core:5.1.3.RELEASE'

    // JAX-RS implementation
    implementation "org.glassfish.jersey.containers:jersey-container-servlet:${jerseyVersion}"
    implementation "org.glassfish.jersey.inject:jersey-hk2:${jerseyVersion}"
    implementation "org.glassfish.jersey.media:jersey-media-multipart:${jerseyVersion}"
    implementation "org.glassfish.jersey.media:jersey-media-json-jackson:${jerseyVersion}"
    implementation "org.glassfish.jersey.media:jersey-media-jaxb:${jerseyVersion}"

    // CDI/Weld support
    implementation 'org.jboss.weld.servlet:weld-servlet:2.4.8.Final'
    runtimeOnly 'org.jboss:jandex:2.4.1.Final'
    runtimeOnly "org.glassfish.jersey.ext.cdi:jersey-cdi1x:${jerseyVersion}"
    runtimeOnly "org.glassfish.jersey.ext.cdi:jersey-cdi1x-servlet:${jerseyVersion}"
    runtimeOnly "org.glassfish.jersey.ext.cdi:jersey-cdi1x-ban-custom-hk2-binding:${jerseyVersion}"

    // needed to load properties, logging configuration injected by CMDB/deploy process
    implementation 'com.tr.bluemoon.brtap:servlet-config:1.0.0'
    implementation 'com.tr.bluemoon.brtap:log4j-jsonevent-layout:1.0.0'

    // Swagger
    // Exclude jackson and provide bumped version to solve Snyk vulnerabilities of this transitive dependency
    implementation ('io.swagger:swagger-jersey2-jaxrs:1.6.14') {
        exclude group: 'com.fasterxml.jackson.dataformat', module: 'jackson-dataformat-yaml'
        exclude group: 'com.fasterxml.jackson.core', module: 'jackson-core'
    }

    implementation 'com.fasterxml.jackson.core:jackson-core:2.15.0'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.0'
    implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.15.0'

    implementation 'com.tr.bluemoon.brtap:rest-commons:1.0.0'
    implementation "com.tr.bluemoon.brtap:commons-security:1.0.0"
    implementation("com.tr.bluemoon.bracct:bracct-commons:${bracctCommonsVersion}") {
        exclude group: 'org.bouncycastle', module: 'bcprov-jdk15on'
    }
    implementation "org.bouncycastle:bcprov-jdk18on:1.80"
    //TODO
    implementation "org.apache.httpcomponents:httpclient:4.5.14"
    implementation("com.tr.bluemoon.bracct:bracct-commons-rest:${bracctCommonsRestVersion}") {
        exclude group: 'com.tr.bluemoon', module: 'rest-security'
        exclude group: 'org.postgresql', module: 'postgresql'
        exclude group: 'org.bouncycastle', module: 'bcprov-jdk15on'
    }
    implementation('com.tr.bluemoon.brtap:rest-security:1.0.0') {
        exclude group: 'com.thomsonreuters', module: 'domain-awareness'
    }

    testImplementation 'org.mockito:mockito-inline:4.0.0'
}

task cleanTomcat(type: Delete, dependsOn: clean) {
    def dir = System.getenv("CATALINA_HOME") ?: catalina_home
    delete "${dir}/webapps/api#${webContext}"
}

task unwar(type: Copy, dependsOn: assemble) {
    // Use the environmental variable if set or fall back to the properties file entry
    def dir = System.getenv("CATALINA_HOME") ?: catalina_home
    from zipTree(file("build/libs/${war.archiveName}"))
    into file("${dir}/webapps/api#${webContext}")
}

task deployCi {
    inputs.files war
    outputs.upToDateWhen { false }

    doLast {
        def parent = "" // use for testing task locally (Place your home directory)
        def vertical = "BRAccountingEntryIntegrationServices"
        def buildVersion = System.getenv()['BUILD_NUMBER']
        if (buildVersion.length() > 0) {
            def ciToken = "WeHaveMoreIntegrityThanKnowledge"
            def skid = new File("$parent/mnt/coderelease/$vertical/$buildVersion")
            skid.mkdirs()
            def versionText = new File("$parent/mnt/coderelease/$vertical/version_ci.txt")
            versionText.withWriter { it << "$vertical=$buildVersion" }
            def propertiesFile = new File("$skid/${vertical}.properties").createNewFile()
            logger.info("Copying jar to ${skid.getCanonicalPath()}")

            copy {
                from war
                into skid
                include '**/*.war'
                rename ".+\\.war", "${vertical}.war"
            }
            def response = "http://bluemooncistools.int.thomsonreuters.com/jenkins/job/$vertical/build?token=$ciToken".toURL().text
            println "Trigger sent to http://bluemooncistools.int.thomsonreuters.com/jenkins/job/$vertical"
        }
    }
}