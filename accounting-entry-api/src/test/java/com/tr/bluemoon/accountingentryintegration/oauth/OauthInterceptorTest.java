package com.tr.bluemoon.accountingentryintegration.oauth;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import com.auth0.jwk.Jwk;
import com.auth0.jwk.JwkException;
import com.auth0.jwk.JwkProvider;
import com.auth0.jwt.algorithms.Algorithm;
import com.tr.bluemoon.bracct.commons.SecurityInfo;
import com.tr.bluemoon.bracct.commons.util.UuidUtils;
import com.tr.bluemoon.commons.token.authentication.AuthenticationException;
import java.util.UUID;
import javax.ws.rs.ForbiddenException;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ResourceInfo;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

class OauthInterceptorTest {

  public static final String INTEGRATION_KEY_HEADER = "x-integration-key";
  public static final String COMPANY_ID_HEADER = "x-company-id";
  private static final String NO_EXCEPTION_SHOULD_BE_THROWN = "No exception should be thrown";

  @Test
  void testInstantiation() {
    final var oauth = new OauthStatusV1Dto();
    final var path = new Path();
    final var defProvider = new DefaultProvider();
    final var env = new Environment();
    final var server = new TrServer();

    oauth.setMessage("message");
    path.setHost("host");
    defProvider.setBasePath("basePath");
    env.setTest(path);
    server.setDefault(defProvider);

    Assertions.assertEquals("message", oauth.getMessage());
    Assertions.assertEquals("host", path.getHost());
    Assertions.assertEquals("basePath", defProvider.getBasePath());
    Assertions.assertEquals(path, env.getTest());
    Assertions.assertEquals(443, path.getPort());
    Assertions.assertTrue(path.getSsl());
    Assertions.assertEquals(defProvider, server.getDefault());
  }

  @Test
  void shouldSkipFilterWhenItIsNotExternalApi() throws NoSuchMethodException {
    // Arrange
    final var resourceInfo = Mockito.mock(ResourceInfo.class);
    final var context = Mockito.mock(ContainerRequestContext.class);
    final var interceptor = Mockito.spy(new OauthInterceptor(resourceInfo));

    Mockito.when(resourceInfo.getResourceMethod())
        .thenReturn(this.getClass().getDeclaredMethod("shouldSkipFilterWhenItIsNotExternalApi"));

    // Action
    interceptor.filter(context);

    // Assertions
    Mockito.verify(interceptor, Mockito.never()).processCompanyRelatedHeaders(Mockito.any());
    Mockito.verify(interceptor, Mockito.never())
        .processMachineToMachineToken(Mockito.any(), Mockito.any());
    Mockito.verify(interceptor, Mockito.never()).processAuthorizationHeader(Mockito.any());
  }

  @Test
  @ExternalApi(MachineToMachine = true)
  void shouldFilterMachineToMachineWhenExternalApiIsMachineToMachine()
      throws NoSuchMethodException {
    // Arrange
    final var resourceInfo = Mockito.mock(ResourceInfo.class);
    final var context = Mockito.mock(ContainerRequestContext.class);
    final var interceptor = Mockito.spy(new OauthInterceptor(resourceInfo));

    final var resourceMethod =
        this.getClass()
            .getDeclaredMethod("shouldFilterMachineToMachineWhenExternalApiIsMachineToMachine");
    final var annotation = resourceMethod.getAnnotation(ExternalApi.class);
    Mockito.when(resourceInfo.getResourceMethod()).thenReturn(resourceMethod);
    Mockito.doNothing().when(interceptor).processCompanyRelatedHeaders(Mockito.any());
    Mockito.doNothing()
        .when(interceptor)
        .processMachineToMachineToken(Mockito.any(), Mockito.any());
    Mockito.doNothing().when(interceptor).processAuthorizationHeader(Mockito.any());

    // Action
    interceptor.filter(context);

    // Assertions
    Mockito.verify(interceptor).processCompanyRelatedHeaders(context);
    Mockito.verify(interceptor).processMachineToMachineToken(context, annotation);
    Mockito.verify(interceptor, Mockito.never()).processAuthorizationHeader(Mockito.any());
  }

  @Test
  @ExternalApi
  void shouldFilterAuthorizationHeaderWhenExternalApiIsMachineToMachineFalse()
      throws NoSuchMethodException {
    // Arrange
    final var resourceInfo = Mockito.mock(ResourceInfo.class);
    final var context = Mockito.mock(ContainerRequestContext.class);
    final var interceptor = Mockito.spy(new OauthInterceptor(resourceInfo));

    final var resourceMethod =
        this.getClass()
            .getDeclaredMethod(
                "shouldFilterAuthorizationHeaderWhenExternalApiIsMachineToMachineFalse");
    final var annotation = resourceMethod.getAnnotation(ExternalApi.class);
    Mockito.when(resourceInfo.getResourceMethod()).thenReturn(resourceMethod);
    Mockito.doNothing().when(interceptor).processCompanyRelatedHeaders(Mockito.any());
    Mockito.doNothing()
        .when(interceptor)
        .processMachineToMachineToken(Mockito.any(), Mockito.any());
    Mockito.doNothing().when(interceptor).processAuthorizationHeader(Mockito.any());

    // Action
    interceptor.filter(context);

    // Assertions
    Mockito.verify(interceptor, Mockito.never()).processCompanyRelatedHeaders(Mockito.any());
    Mockito.verify(interceptor, Mockito.never())
        .processMachineToMachineToken(Mockito.any(), Mockito.any());
    Mockito.verify(interceptor).processAuthorizationHeader(context);
  }

  @Test
  void shouldGenerateNewAutDataAndSetDataToRedisWhenLongTokenIsNotValid() {
    // Arrange
    final ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final OauthInterceptor interceptor = Mockito.spy(new OauthInterceptor(resourceInfo));

    final String authorization = "Bearer 340d5708-f14f-4086-acd9-8f4f26c27ccd";
    final String clientId = "d70b9bff-55f1-4cee-b6ea-9ec839ba9267";
    final String partnerId = null;
    final String longToken = "cc2ca418-9787-41a7-858b-f03f64adcb1e";
    final boolean isValidToken = false;
    final boolean isValidPartner = false;

    MultivaluedMap<String, String> headers = new MultivaluedHashMap<String, String>();
    headers.add(HttpHeaders.AUTHORIZATION, authorization);
    headers.add("onvio-client-id", clientId);
    headers.add("onvio-partner-id", partnerId);

    SmbAuthV1Dto authDto = new SmbAuthV1Dto();
    authDto.setLongToken(longToken);
    authDto.setClientId(clientId);

    Mockito.doReturn(headers).when(requestContext).getHeaders();
    Mockito.doReturn(authDto).when(interceptor).getAuthDataFromRedis(authorization, clientId);
    Mockito.doReturn(isValidToken).when(interceptor).isValidLongToken(longToken);
    Mockito.doReturn(authDto)
        .when(interceptor)
        .generateAuthData(authorization, clientId, isValidPartner);
    Mockito.doNothing().when(interceptor).setAuthDataToRedis(authDto, authorization, clientId);
    Mockito.doNothing().when(interceptor).setAuthDataToContext(requestContext, authDto);
    // Act
    interceptor.processAuthorizationHeader(requestContext);

    // Assert
    Mockito.verify(interceptor).getAuthDataFromRedis(authorization, clientId);
    Mockito.verify(interceptor).isValidLongToken(longToken);
    Mockito.verify(interceptor).generateAuthData(authorization, clientId, isValidPartner);
    Mockito.verify(interceptor).setAuthDataToRedis(authDto, authorization, clientId);
    Mockito.verify(interceptor).setAuthDataToContext(requestContext, authDto);
  }

  @Test
  void shouldGenerateNewAutDataAndSetDataToRedisWhenDoesNotExists() {
    // Arrange
    final ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final OauthInterceptor interceptor = Mockito.spy(new OauthInterceptor(resourceInfo));

    final String authorization = "Bearer 340d5708-f14f-4086-acd9-8f4f26c27ccd";
    final String clientId = "d70b9bff-55f1-4cee-b6ea-9ec839ba9267";
    final String partnerId = null;
    final String longToken = "cc2ca418-9787-41a7-858b-f03f64adcb1e";
    final boolean isValidToken = true;
    final boolean isValidPartner = false;

    MultivaluedMap<String, String> headers = new MultivaluedHashMap<String, String>();
    headers.add(HttpHeaders.AUTHORIZATION, authorization);
    headers.add("onvio-client-id", clientId);
    headers.add("onvio-partner-id", partnerId);

    SmbAuthV1Dto authDto = new SmbAuthV1Dto();
    authDto.setLongToken(longToken);
    authDto.setClientId(clientId);

    Mockito.doReturn(headers).when(requestContext).getHeaders();
    Mockito.doReturn(null).when(interceptor).getAuthDataFromRedis(authorization, clientId);
    Mockito.doReturn(isValidToken).when(interceptor).isValidLongToken(longToken);
    Mockito.doReturn(authDto)
        .when(interceptor)
        .generateAuthData(authorization, clientId, isValidPartner);
    Mockito.doNothing().when(interceptor).setAuthDataToRedis(authDto, authorization, clientId);
    Mockito.doNothing().when(interceptor).setAuthDataToContext(requestContext, authDto);
    // Act
    interceptor.processAuthorizationHeader(requestContext);

    // Assert
    Mockito.verify(interceptor).getAuthDataFromRedis(authorization, clientId);
    Mockito.verify(interceptor, Mockito.never()).isValidLongToken(longToken);
    Mockito.verify(interceptor).generateAuthData(authorization, clientId, isValidPartner);
    Mockito.verify(interceptor).setAuthDataToRedis(authDto, authorization, clientId);
    Mockito.verify(interceptor).setAuthDataToContext(requestContext, authDto);
  }

  @Test
  void shouldGetDataFromRedisWithoutClientId() {
    // Arrange
    final ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final OauthInterceptor interceptor = Mockito.spy(new OauthInterceptor(resourceInfo));

    final String authorization = "Bearer 340d5708-f14f-4086-acd9-8f4f26c27ccd";
    final String clientId = "d70b9bff-55f1-4cee-b6ea-9ec839ba9267";
    final String partnerId = "edd2b872-bc4c-4ed8-ac38-0628d55d55fe";
    final String longToken = "cc2ca418-9787-41a7-858b-f03f64adcb1e";
    final boolean isValidToken = true;
    final boolean isValidPartner = true;

    MultivaluedMap<String, String> headers = new MultivaluedHashMap<String, String>();
    headers.add(HttpHeaders.AUTHORIZATION, authorization);
    headers.add("onvio-client-id", clientId);
    headers.add("onvio-partner-id", partnerId);

    SmbAuthV1Dto authDto = new SmbAuthV1Dto();
    authDto.setLongToken(longToken);
    authDto.setClientId(clientId);

    Mockito.doReturn(headers).when(requestContext).getHeaders();
    Mockito.doReturn(authDto).when(interceptor).getAuthDataFromRedis(authorization, null);
    Mockito.doReturn(isValidToken).when(interceptor).isValidLongToken(longToken);
    Mockito.doReturn(authDto)
        .when(interceptor)
        .generateAuthData(authorization, null, isValidPartner);
    Mockito.doNothing().when(interceptor).setAuthDataToRedis(authDto, authorization, null);
    Mockito.doNothing().when(interceptor).setAuthDataToContext(requestContext, authDto);
    // Act
    interceptor.processAuthorizationHeader(requestContext);

    // Assert
    Mockito.verify(interceptor).getAuthDataFromRedis(authorization, null);
    Mockito.verify(interceptor).isValidLongToken(longToken);
    Mockito.verify(interceptor, Mockito.never())
        .generateAuthData(authorization, null, isValidPartner);
    Mockito.verify(interceptor, Mockito.never()).setAuthDataToRedis(authDto, authorization, null);
    Mockito.verify(interceptor).setAuthDataToContext(requestContext, authDto);
  }

  @Test
  void shouldGetDataFromRedisWithClientId() {
    // Arrange
    final ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final OauthInterceptor interceptor = Mockito.spy(new OauthInterceptor(resourceInfo));

    final String authorization = "Bearer 340d5708-f14f-4086-acd9-8f4f26c27ccd";
    final String clientId = "d70b9bff-55f1-4cee-b6ea-9ec839ba9267";
    final String partnerId = null;
    final String longToken = "cc2ca418-9787-41a7-858b-f03f64adcb1e";
    final boolean isValidToken = true;
    final boolean isValidPartner = false;

    MultivaluedMap<String, String> headers = new MultivaluedHashMap<String, String>();
    headers.add(HttpHeaders.AUTHORIZATION, authorization);
    headers.add("onvio-client-id", clientId);
    headers.add("onvio-partner-id", partnerId);

    SmbAuthV1Dto authDto = new SmbAuthV1Dto();
    authDto.setLongToken(longToken);
    authDto.setClientId(clientId);

    Mockito.doReturn(headers).when(requestContext).getHeaders();
    Mockito.doReturn(authDto).when(interceptor).getAuthDataFromRedis(authorization, clientId);
    Mockito.doReturn(isValidToken).when(interceptor).isValidLongToken(longToken);
    Mockito.doReturn(authDto)
        .when(interceptor)
        .generateAuthData(authorization, clientId, isValidPartner);
    Mockito.doNothing().when(interceptor).setAuthDataToRedis(authDto, authorization, clientId);
    Mockito.doNothing().when(interceptor).setAuthDataToContext(requestContext, authDto);
    // Act
    interceptor.processAuthorizationHeader(requestContext);

    // Assert
    Mockito.verify(interceptor).getAuthDataFromRedis(authorization, clientId);
    Mockito.verify(interceptor).isValidLongToken(longToken);
    Mockito.verify(interceptor, Mockito.never())
        .generateAuthData(authorization, clientId, isValidPartner);
    Mockito.verify(interceptor, Mockito.never())
        .setAuthDataToRedis(authDto, authorization, clientId);
    Mockito.verify(interceptor).setAuthDataToContext(requestContext, authDto);
  }

  @Test
  void shouldDoNothingWhenAuthorizationIsNotAJWT() {
    // Arrange
    final ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final OauthInterceptor interceptor = Mockito.spy(new OauthInterceptor(resourceInfo));

    final String authorization = "UDSLongToken 340d5708-f14f-4086-acd9-8f4f26c27ccd";
    final String clientId = "d70b9bff-55f1-4cee-b6ea-9ec839ba9267";
    final String partnerId = "edd2b872-bc4c-4ed8-ac38-0628d55d55fe";
    final String longToken = "cc2ca418-9787-41a7-858b-f03f64adcb1e";
    final boolean isValidToken = true;
    final boolean isValidPartner = true;

    MultivaluedMap<String, String> headers = new MultivaluedHashMap<String, String>();
    headers.add(HttpHeaders.AUTHORIZATION, authorization);
    headers.add("onvio-client-id", clientId);
    headers.add("onvio-partner-id", partnerId);

    SmbAuthV1Dto authDto = new SmbAuthV1Dto();

    Mockito.doReturn(headers).when(requestContext).getHeaders();
    Mockito.doReturn(authDto).when(interceptor).getAuthDataFromRedis(authorization, clientId);
    Mockito.doReturn(isValidToken).when(interceptor).isValidLongToken(longToken);
    Mockito.doReturn(authDto)
        .when(interceptor)
        .generateAuthData(authorization, clientId, isValidPartner);
    Mockito.doNothing().when(interceptor).setAuthDataToRedis(authDto, authorization, clientId);
    Mockito.doNothing().when(interceptor).setAuthDataToContext(requestContext, authDto);
    // Act
    interceptor.processAuthorizationHeader(requestContext);

    // Assert
    Mockito.verify(interceptor, Mockito.never()).getAuthDataFromRedis(authorization, clientId);
    Mockito.verify(interceptor, Mockito.never()).isValidLongToken(longToken);
    Mockito.verify(interceptor, Mockito.never())
        .generateAuthData(authorization, clientId, isValidPartner);
    Mockito.verify(interceptor, Mockito.never())
        .setAuthDataToRedis(authDto, authorization, clientId);
    Mockito.verify(interceptor, Mockito.never()).setAuthDataToContext(requestContext, authDto);
  }

  @Test
  void shouldThrowExceptionWhenTryToProcessAuthorizationHeaderWithoutAuthorizationOnHeader() {
    // Arrange
    final ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final OauthInterceptor interceptor = Mockito.spy(new OauthInterceptor(resourceInfo));

    MultivaluedMap<String, String> headers = new MultivaluedHashMap<String, String>();
    Mockito.doReturn(headers).when(requestContext).getHeaders();

    // Act
    Assertions.assertThrows(
        AuthenticationException.class,
        () -> interceptor.processAuthorizationHeader(requestContext));
  }

  @Test
  void shouldThrowExceptionWhenTryToProcessAuthorizationHeaderWithNullHeaders() {
    // Arrange
    final ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final OauthInterceptor interceptor = Mockito.spy(new OauthInterceptor(resourceInfo));

    MultivaluedMap<String, String> headers = null;
    Mockito.doReturn(headers).when(requestContext).getHeaders();

    // Act
    Assertions.assertThrows(
        AuthenticationException.class,
        () -> interceptor.processAuthorizationHeader(requestContext));
  }

  @Test
  @SuppressWarnings("unchecked")
  void shouldGenerateAuthDataAndThrowExceptionWhenResponseIsNotOk() {
    // Arrange
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final OauthInterceptor interceptor = Mockito.spy(new OauthInterceptor(resourceInfo));

    final String token = "Bearer 340d5708-f14f-4086-acd9-8f4f26c27ccd";
    final String clientId = "d70b9bff-55f1-4cee-b6ea-9ec839ba9267";
    final boolean isValidPartner = false;
    final int status = Status.INTERNAL_SERVER_ERROR.getStatusCode();
    final String body = "internal error";

    WebTarget target = Mockito.mock(WebTarget.class);
    Invocation.Builder builder = Mockito.mock(Invocation.Builder.class);
    Response response = Mockito.mock(Response.class);

    Mockito.doReturn(target).when(interceptor).getWebTargetToGenerateUDS();
    Mockito.doReturn(builder).when(target).request();
    Mockito.doReturn(builder).when(builder).headers(any(MultivaluedMap.class));
    Mockito.doReturn(response).when(builder).post(any());
    Mockito.doReturn(status).when(response).getStatus();
    Mockito.doReturn(body).when(response).readEntity(eq(String.class));

    // Act
    try {
      interceptor.generateAuthData(token, clientId, isValidPartner);
    } catch (AuthenticationException e) {
      // Assert
      Mockito.verify(builder).post(any());
      Mockito.verify(response).close();
      return;
    }

    Assertions.fail("Expected to throw AuthenticationException.");
  }

  @Test
  @SuppressWarnings("unchecked")
  void shouldGenerateAuthDataWithValidPartner() {
    // Arrange
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final OauthInterceptor interceptor = Mockito.spy(new OauthInterceptor(resourceInfo));

    final String token = "Bearer 340d5708-f14f-4086-acd9-8f4f26c27ccd";
    final String clientId = "d70b9bff-55f1-4cee-b6ea-9ec839ba9267";
    final String longToken = "edd2b872-bc4c-4ed8-ac38-0628d55d55fe";
    final boolean isValidPartner = true;
    final int status = Status.OK.getStatusCode();

    SmbAuthV1Dto expected = new SmbAuthV1Dto();
    expected.setLongToken(longToken);
    expected.setClientId(clientId);

    WebTarget target = Mockito.mock(WebTarget.class);
    Invocation.Builder builder = Mockito.mock(Invocation.Builder.class);
    Response response = Mockito.mock(Response.class);

    Mockito.doReturn(target).when(interceptor).getWebTargetToGenerateUDS();
    Mockito.doReturn(builder).when(target).request();
    Mockito.doReturn(builder).when(builder).headers(any(MultivaluedMap.class));
    Mockito.doReturn(response).when(builder).post(any());
    Mockito.doReturn(status).when(response).getStatus();
    Mockito.doReturn(expected).when(response).readEntity(eq(SmbAuthV1Dto.class));

    // Act
    final SmbAuthV1Dto dto = interceptor.generateAuthData(token, clientId, isValidPartner);

    // Assert
    Mockito.verify(builder)
        .headers(
            Mockito.argThat(
                headers -> {
                  String acessToken = (String) headers.getFirst("access-token");
                  boolean validPartner = (boolean) headers.getFirst("valid-partner");
                  String onvioClientId = (String) headers.getFirst("onvio-client-id");
                  if (!token.equals(acessToken)) {
                    return false;
                  }
                  if (isValidPartner != validPartner) {
                    return false;
                  }
                  if (!clientId.equals(onvioClientId)) {
                    return false;
                  }
                  return true;
                }));
    Mockito.verify(builder).post(any());
    Mockito.verify(response).close();
    Assertions.assertNotNull(dto);
    Assertions.assertEquals(clientId, dto.getClientId());
    Assertions.assertEquals(longToken, dto.getLongToken());
  }

  @Test
  @SuppressWarnings("unchecked")
  void shouldGenerateAuthDataWithClientId() {
    // Arrange
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final OauthInterceptor interceptor = Mockito.spy(new OauthInterceptor(resourceInfo));

    final String token = "Bearer 340d5708-f14f-4086-acd9-8f4f26c27ccd";
    final String clientId = "d70b9bff-55f1-4cee-b6ea-9ec839ba9267";
    final String longToken = "edd2b872-bc4c-4ed8-ac38-0628d55d55fe";
    final boolean isValidPartner = false;
    final int status = Status.OK.getStatusCode();

    SmbAuthV1Dto expected = new SmbAuthV1Dto();
    expected.setLongToken(longToken);
    expected.setClientId(clientId);

    WebTarget target = Mockito.mock(WebTarget.class);
    Invocation.Builder builder = Mockito.mock(Invocation.Builder.class);
    Response response = Mockito.mock(Response.class);

    Mockito.doReturn(target).when(interceptor).getWebTargetToGenerateUDS();
    Mockito.doReturn(builder).when(target).request();
    Mockito.doReturn(builder).when(builder).headers(any(MultivaluedMap.class));
    Mockito.doReturn(response).when(builder).post(any());
    Mockito.doReturn(status).when(response).getStatus();
    Mockito.doReturn(expected).when(response).readEntity(eq(SmbAuthV1Dto.class));

    // Act
    final SmbAuthV1Dto dto = interceptor.generateAuthData(token, clientId, isValidPartner);

    // Assert
    Mockito.verify(builder)
        .headers(
            Mockito.argThat(
                headers -> {
                  String acessToken = (String) headers.getFirst("access-token");
                  boolean validPartner = (boolean) headers.getFirst("valid-partner");
                  String onvioClientId = (String) headers.getFirst("onvio-client-id");
                  if (!token.equals(acessToken)) {
                    return false;
                  }
                  if (isValidPartner != validPartner) {
                    return false;
                  }
                  if (!clientId.equals(onvioClientId)) {
                    return false;
                  }
                  return true;
                }));
    Mockito.verify(builder).post(any());
    Mockito.verify(response).close();
    Assertions.assertNotNull(dto);
    Assertions.assertEquals(clientId, dto.getClientId());
    Assertions.assertEquals(longToken, dto.getLongToken());
  }

  @Test
  @SuppressWarnings("unchecked")
  void shouldGenerateAuthDataWithoutClientIdWhenIsEmpty() {
    // Arrange
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final OauthInterceptor interceptor = Mockito.spy(new OauthInterceptor(resourceInfo));

    final String token = "Bearer 340d5708-f14f-4086-acd9-8f4f26c27ccd";
    final String clientId = "";
    final String longToken = "edd2b872-bc4c-4ed8-ac38-0628d55d55fe";
    final boolean isValidPartner = false;
    final int status = Status.OK.getStatusCode();

    SmbAuthV1Dto expected = new SmbAuthV1Dto();
    expected.setLongToken(longToken);
    expected.setClientId(clientId);

    WebTarget target = Mockito.mock(WebTarget.class);
    Invocation.Builder builder = Mockito.mock(Invocation.Builder.class);
    Response response = Mockito.mock(Response.class);

    Mockito.doReturn(target).when(interceptor).getWebTargetToGenerateUDS();
    Mockito.doReturn(builder).when(target).request();
    Mockito.doReturn(builder).when(builder).headers(any(MultivaluedMap.class));
    Mockito.doReturn(response).when(builder).post(any());
    Mockito.doReturn(status).when(response).getStatus();
    Mockito.doReturn(expected).when(response).readEntity(eq(SmbAuthV1Dto.class));

    // Act
    final SmbAuthV1Dto dto = interceptor.generateAuthData(token, clientId, isValidPartner);

    // Assert
    Mockito.verify(builder)
        .headers(
            Mockito.argThat(
                headers -> {
                  String acessToken = (String) headers.getFirst("access-token");
                  boolean validPartner = (boolean) headers.getFirst("valid-partner");

                  if (!token.equals(acessToken)) {
                    return false;
                  }
                  if (isValidPartner != validPartner) {
                    return false;
                  }
                  if (headers.containsKey("onvio-client-id")) {
                    return false;
                  }
                  return true;
                }));
    Mockito.verify(builder).post(any());
    Mockito.verify(response).close();
    Assertions.assertNotNull(dto);
    Assertions.assertEquals(clientId, dto.getClientId());
    Assertions.assertEquals(longToken, dto.getLongToken());
  }

  @Test
  @SuppressWarnings("unchecked")
  void shouldGenerateAuthDataWithoutClientIdWhenIsNull() {
    // Arrange
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final OauthInterceptor interceptor = Mockito.spy(new OauthInterceptor(resourceInfo));

    final String token = "Bearer 340d5708-f14f-4086-acd9-8f4f26c27ccd";
    final String clientId = null;
    final String longToken = "edd2b872-bc4c-4ed8-ac38-0628d55d55fe";
    final boolean isValidPartner = false;
    final int status = Status.OK.getStatusCode();

    SmbAuthV1Dto expected = new SmbAuthV1Dto();
    expected.setLongToken(longToken);
    expected.setClientId(clientId);

    WebTarget target = Mockito.mock(WebTarget.class);
    Invocation.Builder builder = Mockito.mock(Invocation.Builder.class);
    Response response = Mockito.mock(Response.class);

    Mockito.doReturn(target).when(interceptor).getWebTargetToGenerateUDS();
    Mockito.doReturn(builder).when(target).request();
    Mockito.doReturn(builder).when(builder).headers(any(MultivaluedMap.class));
    Mockito.doReturn(response).when(builder).post(any());
    Mockito.doReturn(status).when(response).getStatus();
    Mockito.doReturn(expected).when(response).readEntity(SmbAuthV1Dto.class);

    // Act
    final SmbAuthV1Dto dto = interceptor.generateAuthData(token, clientId, isValidPartner);

    // Assert
    Mockito.verify(builder)
        .headers(
            Mockito.argThat(
                headers -> {
                  String acessToken = (String) headers.getFirst("access-token");
                  boolean validPartner = (boolean) headers.getFirst("valid-partner");

                  if (!token.equals(acessToken)) {
                    return false;
                  }
                  if (isValidPartner != validPartner) {
                    return false;
                  }
                  if (headers.containsKey("onvio-client-id")) {
                    return false;
                  }
                  return true;
                }));
    Mockito.verify(builder).post(any());
    Mockito.verify(response).close();
    Assertions.assertNotNull(dto);
    Assertions.assertEquals(clientId, dto.getClientId());
    Assertions.assertEquals(longToken, dto.getLongToken());
  }

  @Test
  void shouldGetKeyWithClientId() {
    // Arrange
    ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    OauthInterceptor interceptor = new OauthInterceptor(resourceInfo);

    String token = "Bearer 340d5708-f14f-4086-acd9-8f4f26c27ccd";
    String clientId = "edd2b872-bc4c-4ed8-ac38-0628d55d55fe";
    String expected =
        "OnvioBrOauth2:b97f320b6a8ae91027ccf267ccc4072d:edd2b872-bc4c-4ed8-ac38-0628d55d55fe";

    // Act
    String key = interceptor.getKey(token, clientId);

    // Assert
    Assertions.assertEquals(expected, key);
  }

  @Test
  void shouldGetKeyWithoutClientIdWhenIsEmpty() {
    // Arrange
    ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    OauthInterceptor interceptor = new OauthInterceptor(resourceInfo);

    String token = "Bearer 340d5708-f14f-4086-acd9-8f4f26c27ccd";
    String clientId = "";
    String expected = "OnvioBrOauth2:b97f320b6a8ae91027ccf267ccc4072d";

    // Act
    String key = interceptor.getKey(token, clientId);

    // Assert
    Assertions.assertEquals(expected, key);
  }

  @Test
  void shouldGetKeyWithoutClientIdWhenIsNull() {
    // Arrange
    ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    OauthInterceptor interceptor = new OauthInterceptor(resourceInfo);

    String token = "Bearer 340d5708-f14f-4086-acd9-8f4f26c27ccd";
    String clientId = null;
    String expected = "OnvioBrOauth2:b97f320b6a8ae91027ccf267ccc4072d";

    // Act
    String key = interceptor.getKey(token, clientId);

    // Assert
    Assertions.assertEquals(expected, key);
  }

  @Test
  void shouldSetAuthDataAuthorizationAndClientToContext() {
    // Arrange
    final ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final OauthInterceptor interceptor = new OauthInterceptor(resourceInfo);

    final SmbAuthV1Dto authDto = new SmbAuthV1Dto();
    authDto.setClientId(UuidUtils.fromUUID(UUID.randomUUID()));
    authDto.setLongToken(UuidUtils.fromUUID(UUID.randomUUID()));

    MultivaluedMap<String, String> headers = new MultivaluedHashMap<>();

    Mockito.doReturn(headers).when(requestContext).getHeaders();
    String expected = "UDSLongToken " + authDto.getLongToken();

    // Act
    interceptor.setAuthDataToContext(requestContext, authDto);

    // Assert
    Assertions.assertEquals(expected, headers.getFirst(HttpHeaders.AUTHORIZATION));
    Assertions.assertEquals(
        authDto.getClientId(), headers.getFirst(OauthInterceptor.ONVIO_CLIENT_HEADER));
  }

  @Test
  void shouldSetAuthDataOnlyAuthorizationToContextWhenClientIdIsNull() {
    // Arrange
    final ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final OauthInterceptor interceptor = new OauthInterceptor(resourceInfo);

    final SmbAuthV1Dto authDto = new SmbAuthV1Dto();
    authDto.setClientId(null);
    authDto.setLongToken(UuidUtils.fromUUID(UUID.randomUUID()));

    MultivaluedMap<String, String> headers = new MultivaluedHashMap<>();

    Mockito.doReturn(headers).when(requestContext).getHeaders();
    String expected = "UDSLongToken " + authDto.getLongToken();

    // Act
    interceptor.setAuthDataToContext(requestContext, authDto);

    // Assert
    Assertions.assertEquals(expected, headers.getFirst(HttpHeaders.AUTHORIZATION));
    Assertions.assertNull(headers.get(OauthInterceptor.ONVIO_CLIENT_HEADER));
  }

  @Test
  void shouldSetAuthDataOnlyAuthorizationToContextWhenClientIdIsEmpty() {
    // Arrange
    final ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final OauthInterceptor interceptor = new OauthInterceptor(resourceInfo);

    final SmbAuthV1Dto authDto = new SmbAuthV1Dto();
    authDto.setClientId("");
    authDto.setLongToken(UuidUtils.fromUUID(UUID.randomUUID()));

    MultivaluedMap<String, String> headers = new MultivaluedHashMap<>();

    Mockito.doReturn(headers).when(requestContext).getHeaders();
    String expected = "UDSLongToken " + authDto.getLongToken();

    // Act
    interceptor.setAuthDataToContext(requestContext, authDto);

    // Assert
    Assertions.assertEquals(expected, headers.getFirst(HttpHeaders.AUTHORIZATION));
    Assertions.assertNull(headers.get(OauthInterceptor.ONVIO_CLIENT_HEADER));
  }

  @Test
  void shouldConvertToMD5() {
    // Arrange
    ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    OauthInterceptor interceptor = new OauthInterceptor(resourceInfo);

    String expected = "b97f320b6a8ae91027ccf267ccc4072d";

    // Act
    String actual = interceptor.getMd5("340d5708-f14f-4086-acd9-8f4f26c27ccd");

    // Assert
    Assertions.assertEquals(expected, actual);
  }

  @Test
  void shouldBeValidPartner() {
    // Arrange
    ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    OauthInterceptor interceptor = new OauthInterceptor(resourceInfo);

    // Act
    boolean isValidPartner = interceptor.isValidPartner("edd2b872-bc4c-4ed8-ac38-0628d55d55fe");

    // Assert
    Assertions.assertTrue(isValidPartner);
  }

  @Test
  void shouldNotBeValidPartner() {
    // Arrange
    ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    OauthInterceptor interceptor = new OauthInterceptor(resourceInfo);

    // Act
    boolean isValidPartner = interceptor.isValidPartner(UuidUtils.fromUUID(UUID.randomUUID()));

    // Assert
    Assertions.assertFalse(isValidPartner);
  }

  @Test
  void shouldNotBeValidPartnerWhenIsNull() {
    // Arrange
    ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    OauthInterceptor interceptor = new OauthInterceptor(resourceInfo);

    // Act
    boolean isValidPartner = interceptor.isValidPartner(null);

    // Assert
    Assertions.assertFalse(isValidPartner);
  }

  @Test
  void shouldNotBeValidPartnerWhenIsEmpty() {
    // Arrange
    ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    OauthInterceptor interceptor = new OauthInterceptor(resourceInfo);

    // Act
    boolean isValidPartner = interceptor.isValidPartner("");

    // Assert
    Assertions.assertFalse(isValidPartner);
  }

  @Test
  void shouldNotBeValidPartnerWhenIsInvalidUuid() {
    // Arrange
    ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    OauthInterceptor interceptor = new OauthInterceptor(resourceInfo);

    // Act
    boolean isValidPartner = interceptor.isValidPartner("340d5708-f14f-");

    // Assert
    Assertions.assertFalse(isValidPartner);
  }

  @Test
  void shouldValidateJwtWhenItsM2MToken() throws JwkException {
    // Arrange
    final ExternalApi externalApi = Mockito.mock(ExternalApi.class);
    final ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    final Algorithm algorithm = Mockito.mock(Algorithm.class);
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final JwkProvider jwkProvider = Mockito.mock(JwkProvider.class);
    final Jwk jwk = Mockito.mock(Jwk.class);
    final OauthInterceptor interceptor =
        Mockito.spy(new OauthInterceptor(resourceInfo, jwkProvider));

    Mockito.doReturn(algorithm).when(interceptor).getAlgorithm(any(Jwk.class));
    Mockito.doReturn("HS256").when(algorithm).getName();
    String authorization =
        "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";
    MultivaluedMap<String, String> headers = new MultivaluedHashMap<String, String>();
    headers.add(HttpHeaders.AUTHORIZATION, authorization);
    Mockito.doReturn(headers).when(requestContext).getHeaders();
    Mockito.doReturn(jwk).when(jwkProvider).get(any());

    // Act
    interceptor.processMachineToMachineToken(requestContext, externalApi);

    // Assert
    Mockito.verify(algorithm).verify(any());
  }

  @Test
  void shouldThrowErrorWhenItsInvalidJwtWhenItsM2MToken() throws JwkException {
    // Arrange
    final ExternalApi externalApi = Mockito.mock(ExternalApi.class);
    final ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    final Algorithm algorithm = Mockito.mock((Algorithm.class));
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final JwkProvider jwkProvider = Mockito.mock(JwkProvider.class);
    final Jwk jwk = Mockito.mock(Jwk.class);
    final OauthInterceptor interceptor =
        Mockito.spy(new OauthInterceptor(resourceInfo, jwkProvider));

    Mockito.doReturn(algorithm).when(interceptor).getAlgorithm(any(Jwk.class));
    String authorization = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9";
    MultivaluedMap<String, String> headers = new MultivaluedHashMap<String, String>();
    headers.add(HttpHeaders.AUTHORIZATION, authorization);
    Mockito.doReturn(headers).when(requestContext).getHeaders();
    Mockito.doReturn(jwk).when(jwkProvider).get(any());

    // Act
    Assertions.assertThrows(
        AuthenticationException.class,
        () -> interceptor.processMachineToMachineToken(requestContext, externalApi));
  }

  @Test
  void shouldValidateWhenIntegrationKeyIsNull() {
    // Arrange
    ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    JwkProvider jwkProvider = Mockito.mock(JwkProvider.class);
    OauthInterceptor interceptor = Mockito.spy(new OauthInterceptor(resourceInfo, jwkProvider));

    ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);

    doReturn(null).when(requestContext).getHeaders();

    // Act
    Assertions.assertThrows(
        AuthenticationException.class,
        () -> interceptor.processCompanyRelatedHeaders(requestContext));
  }

  @Test
  void shouldValidateIntegrationKeyInvalid() {
    // Arrange
    OauthInterceptor interceptor = Mockito.spy(new OauthInterceptor());
    ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    String authorization = "jFqJFrCRRfikkRJLB6wE_ZNdtc4D_kpnlcFxQQRZmU4";

    MultivaluedMap<String, String> headers = new MultivaluedHashMap<>();
    doReturn(headers).when(requestContext).getHeaders();

    // Act
    Assertions.assertThrows(
        AuthenticationException.class,
        () -> interceptor.processCompanyRelatedHeaders(requestContext));

    // Assert
    verify(interceptor, never()).getCompanyIdFromActivationToken(authorization);
  }

  @Test
  void shouldProcessAuthorizationByIntegrationKey() {
    // Arrange
    ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    JwkProvider jwkProvider = Mockito.mock(JwkProvider.class);
    OauthInterceptor interceptor = Mockito.spy(new OauthInterceptor(resourceInfo, jwkProvider));
    ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    String integratioHeader = "jFqJFrCRRfikkRJLB6wE_ZNdtc4D_kpnlcFxQQRZmU4";

    MultivaluedMap<String, String> headers = new MultivaluedHashMap<>();
    headers.add(INTEGRATION_KEY_HEADER, integratioHeader);

    doReturn(headers).when(requestContext).getHeaders();

    // Act
    interceptor.processCompanyRelatedHeaders(requestContext);

    // Assert
    verify(interceptor).getCompanyIdFromActivationToken(integratioHeader);
  }

  @Test
  void shouldProcessAuthorizationByCompanyId() {
    // Arrange
    ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    JwkProvider jwkProvider = Mockito.mock(JwkProvider.class);
    OauthInterceptor interceptor = Mockito.spy(new OauthInterceptor(resourceInfo, jwkProvider));
    ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    UUID companyId = UUID.randomUUID();

    MultivaluedMap<String, String> headers = new MultivaluedHashMap<>();
    headers.add(COMPANY_ID_HEADER, companyId.toString());

    doReturn(headers).when(requestContext).getHeaders();

    // Act
    interceptor.processCompanyRelatedHeaders(requestContext);

    // Assert
    ArgumentCaptor<SecurityInfo> argument = ArgumentCaptor.forClass(SecurityInfo.class);
    verify(requestContext).setProperty(eq(SecurityInfo.CONTEXT_PROPERTY), argument.capture());
    Assertions.assertEquals(companyId, argument.getValue().getCompanyId());
  }

  @Test
  void shouldRemovePartnerIdHeader() throws JwkException {
    // Assert
    final ExternalApi externalApi = Mockito.mock(ExternalApi.class);
    final ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    final Algorithm algorithm = Mockito.mock((Algorithm.class));
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final JwkProvider jwkProvider = Mockito.mock(JwkProvider.class);
    final Jwk jwk = Mockito.mock(Jwk.class);
    final OauthInterceptor interceptor =
        Mockito.spy(new OauthInterceptor(resourceInfo, jwkProvider));

    Mockito.doReturn(algorithm).when(interceptor).getAlgorithm(any(Jwk.class));
    Mockito.doReturn("HS256").when(algorithm).getName();
    String authorization =
        "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************.n1qDJxnLd4ygYkWJuXVynLrj-JVwh8kr41cSvC8lDUw";
    MultivaluedMap<String, String> headers = new MultivaluedHashMap<String, String>();
    headers.add(HttpHeaders.AUTHORIZATION, authorization);
    Mockito.doReturn(headers).when(requestContext).getHeaders();
    Mockito.doReturn(jwk).when(jwkProvider).get(any());

    requestContext.getHeaders().add("onvio-partner-id", " value 1");
    requestContext.getHeaders().add("onvio-partner-id", " value 2");
    requestContext.getHeaders().add("onvio-partner-id", " value 3");
    requestContext.getHeaders().add("onvio-partner-id", " value 4");
    requestContext.getHeaders().add("onvio-partner-id", " value 5");

    // Act
    interceptor.processMachineToMachineToken(requestContext, externalApi);

    // Assert
    Assertions.assertEquals(1, requestContext.getHeaders().get("onvio-partner-id").size());
    Assertions.assertEquals(
        "client_id5050", requestContext.getHeaders().getFirst("onvio-partner-id"));
  }

  @Test
  void shouldValidateScopesWithNullClaim() throws JwkException {
    // Assert
    final ExternalApi externalApi = Mockito.mock(ExternalApi.class);
    final ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    final Algorithm algorithm = Mockito.mock((Algorithm.class));
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final JwkProvider jwkProvider = Mockito.mock(JwkProvider.class);
    final Jwk jwk = Mockito.mock(Jwk.class);
    final OauthInterceptor interceptor =
        Mockito.spy(new OauthInterceptor(resourceInfo, jwkProvider));

    Mockito.doReturn(new ApiScope[] {ApiScope.ACCOUNTING_ENTRY_READ})
        .when(externalApi)
        .AllowedScopes();
    Mockito.doReturn(algorithm).when(interceptor).getAlgorithm(any(Jwk.class));
    Mockito.doReturn("HS256").when(algorithm).getName();
    String authorization =
        "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************.n1qDJxnLd4ygYkWJuXVynLrj-JVwh8kr41cSvC8lDUw";
    MultivaluedMap<String, String> headers = new MultivaluedHashMap<String, String>();
    headers.add(HttpHeaders.AUTHORIZATION, authorization);
    Mockito.doReturn(headers).when(requestContext).getHeaders();
    Mockito.doReturn(jwk).when(jwkProvider).get(any());

    // Act
    Assertions.assertThrows(
        ForbiddenException.class,
        () -> interceptor.processMachineToMachineToken(requestContext, externalApi));
  }

  @Test
  void shouldValidateScopesWithIncorrectScope() throws JwkException {
    // Assert
    final ExternalApi externalApi = Mockito.mock(ExternalApi.class);
    final ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    final Algorithm algorithm = Mockito.mock((Algorithm.class));
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final JwkProvider jwkProvider = Mockito.mock(JwkProvider.class);
    final Jwk jwk = Mockito.mock(Jwk.class);
    final OauthInterceptor interceptor =
        Mockito.spy(new OauthInterceptor(resourceInfo, jwkProvider));

    Mockito.doReturn(new ApiScope[] {ApiScope.ACCOUNTING_ENTRY_READ})
        .when(externalApi)
        .AllowedScopes();
    Mockito.doReturn(algorithm).when(interceptor).getAlgorithm(any(Jwk.class));
    Mockito.doReturn("HS256").when(algorithm).getName();
    String authorization =
        "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
            + ".eyJhenAiOiJxWmg4YXJyRm81MEppd3Jaek9GYTVvT3lTeHdLb0hCTyIsInNjb3"
            + "BlIjoiaHR0cHM6Ly9hcGkudGhvbXNvbnJldXRlcnMuY29tL2F1dGgvb252aW8tY"
            + "nIuaW52b2ljZS1pbnRlZ3JhdGlvbi53cml0ZSBodHRwczovL2FwaS50aG9tc29u"
            + "cmV1dGVycy5jb20vYXV0aC9vbnZpby1ici5pbnZvaWNlLWludGVncmF0aW9uLnJ"
            + "lYWQiLCJndHkiOiJjbGllbnQtY3JlZGVudGlhbHMifQ.KGNmPYSaxZabq14lbrD"
            + "O3coozBz8ciYI1lqJHS5JY58";
    MultivaluedMap<String, String> headers = new MultivaluedHashMap<String, String>();
    headers.add(HttpHeaders.AUTHORIZATION, authorization);
    Mockito.doReturn(headers).when(requestContext).getHeaders();
    Mockito.doReturn(jwk).when(jwkProvider).get(any());

    // Act
    Assertions.assertThrows(
        ForbiddenException.class,
        () -> interceptor.processMachineToMachineToken(requestContext, externalApi));
  }

  @Test
  void shouldValidateScopesWithCorrectScope() throws JwkException {
    // Assert
    final ExternalApi externalApi = Mockito.mock(ExternalApi.class);
    final ContainerRequestContext requestContext = Mockito.mock(ContainerRequestContext.class);
    final Algorithm algorithm = Mockito.mock((Algorithm.class));
    final ResourceInfo resourceInfo = Mockito.mock(ResourceInfo.class);
    final JwkProvider jwkProvider = Mockito.mock(JwkProvider.class);
    final Jwk jwk = Mockito.mock(Jwk.class);
    final OauthInterceptor interceptor =
        Mockito.spy(new OauthInterceptor(resourceInfo, jwkProvider));

    Mockito.doReturn(new ApiScope[] {ApiScope.ACCOUNTING_ENTRY_READ})
        .when(externalApi)
        .AllowedScopes();
    Mockito.doReturn(algorithm).when(interceptor).getAlgorithm(any(Jwk.class));
    Mockito.doReturn("HS256").when(algorithm).getName();
    String authorization =
        "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
            + "eyJhenAiOiJxWmg4YXJyRm81MEppd3Jaek9GYTVv"
            + "T3lTeHdLb0hCTyIsInNjb3BlIjoiaHR0cHM6Ly9h"
            + "cGkudGhvbXNvbnJldXRlcnMuY29tL2F1dGgvb252"
            + "aW8tYnIuYWNjb3VudGluZy1lbnRyeS1pbnRlZ3Jh"
            + "dGlvbi5yZWFkIiwiZ3R5IjoiY2xpZW50LWNyZWRl"
            + "bnRpYWxzIn0.zyuEIs-ho5cMveKqxhaZKD2YHkBJ3n-8_iS1BiC2oQ4";
    MultivaluedMap<String, String> headers = new MultivaluedHashMap<String, String>();
    headers.add(HttpHeaders.AUTHORIZATION, authorization);
    Mockito.doReturn(headers).when(requestContext).getHeaders();
    Mockito.doReturn(jwk).when(jwkProvider).get(any());

    // Act
    // Assert
    try {
      interceptor.processMachineToMachineToken(requestContext, externalApi);
    } catch (ForbiddenException e) {
      Assertions.fail(NO_EXCEPTION_SHOULD_BE_THROWN);
    }
  }

  @Test
  void sanitizeTokenWithSpecialCharacters() {
    OauthInterceptor interceptor = new OauthInterceptor();
    String token = "Bearer!@#340d5708-f14f-4086-acd9_8f4f26c27ccd";
    String expected = "Bearer 340d5708-f14f-4086-acd9_8f4f26c27ccd";
    Assertions.assertEquals(expected, interceptor.sanitizeToken(token));
  }

  @Test
  void sanitizeTokenWithNull() {
    OauthInterceptor interceptor = new OauthInterceptor();
    Assertions.assertNull(interceptor.sanitizeToken(null));
  }

  @Test
  void sanitizeTokenWithAlphanumericAndPeriods() {
    OauthInterceptor interceptor = new OauthInterceptor();
    String token = "Bearer.340d5708-f14f-4086.acd9-8f4f26c27ccd";
    String expected = "Bearer .340d5708-f14f-4086.acd9-8f4f26c27ccd";
    Assertions.assertEquals(expected, interceptor.sanitizeToken(token));
  }

  @Test
  void sanitizeUUIDWithValidUUID() {
    OauthInterceptor interceptor = new OauthInterceptor();
    String uuid = "340d5708-f14f-4086-acd9-8f4f26c27ccd";
    String expected = "340d5708-f14f-4086-acd9-8f4f26c27ccd";
    Assertions.assertEquals(expected, interceptor.sanitizeUUID(uuid));
  }

  @Test
  void sanitizeUUIDWithInvalidUUID() {
    OauthInterceptor interceptor = new OauthInterceptor();
    String invalidUuid = "invalid-uuid";
    Assertions.assertThrows(IllegalArgumentException.class, () -> interceptor.sanitizeUUID(invalidUuid));
  }
}
