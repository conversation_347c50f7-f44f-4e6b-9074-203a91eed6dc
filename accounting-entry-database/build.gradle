apply plugin: 'java-library'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'

bootJar {
    enabled = false
}

dependencies {
    implementation ('com.tr.bluemoon.brtap:database-sdk:1.0.0') {
        exclude group: 'org.postgresql', module: 'postgresql'
    }
    implementation "com.tr.bluemoon.brtap:commons-utils:${platformCommonsVersion}"
    implementation 'org.apache.logging.log4j:log4j-slf4j-impl:2.17.2'
    implementation 'com.google.guava:guava:33.4.8-jre'

    implementation ("com.tr.bluemoon.bracct:bracct-commons-jpa:${bracctCommonsJpaVersion}")  {
        exclude group: 'org.postgresql', module: 'postgresql'
        exclude group: 'org.bouncycastle', module: 'bcprov-jdk15on'
    }
    implementation "org.postgresql:postgresql:${postgresqlVersion}"
    implementation "org.bouncycastle:bcprov-jdk18on:1.80"

    // JPA implementation
    implementation ("org.eclipse.persistence:eclipselink:${eclipseLinkVersion}") {
        exclude group: 'org.eclipse.persistence', module: 'javax.persistence'
    }
}