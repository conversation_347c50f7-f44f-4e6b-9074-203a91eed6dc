package com.tr.bluemoon.accountingentryintegration.database;

import com.tr.bluemoon.commons.utils.LookupUtil;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import javax.persistence.EntityManager;

public class BluemoonBucketPersistenceManager {

  // Fallback database connection if CMDB value isn't set
  private static final String DEFAULT_DATASOURCE = "bm_wsd_lookup";
  // Persistence unit in persistence.xml file
  private static final String PERSISTENCE_UNIT = "BUCKETING";
  private static Map<String, PersistenceManager> PROVIDERS = new ConcurrentHashMap<>();

  private synchronized PersistenceManager addProvider(final String datasource) {
    final PersistenceManager provider =
        PersistenceManager.createPersistenceManager(datasource, PERSISTENCE_UNIT);
    if (provider != null) {
      PROVIDERS.put(datasource, provider);
      return PROVIDERS.get(datasource);
    } else {
      throw new IllegalArgumentException("Database URL not defined");
    }
  }

  private String getDatasource(String datasourceKey) {
    return LookupUtil.get(datasourceKey, DEFAULT_DATASOURCE);
  }

  private PersistenceManager getProvider(String datasourceKey) {
    final String datasource = getDatasource(datasourceKey);
    final PersistenceManager provider = PROVIDERS.get(datasource);
    return (provider == null) ? addProvider(datasource) : provider;
  }

  public void close(String datasourceKey) {
    getProvider(datasourceKey).close();
  }

  public EntityManager createEntityManager(String datasourceKey) {
    return getProvider(datasourceKey).createEntityManager();
  }

  public EntityManager getEntityManager(String datasourceKey) {
    return getProvider(datasourceKey).getEntityManager();
  }

  public String getConnectionTestQuery(String datasourceKey) {
    return getProvider(datasourceKey).getConnectionTestQuery();
  }

  public Object getDefinitionOfFalse(String datasourceKey) {
    return getProvider(datasourceKey).getDefinitionOfFalse();
  }
}
