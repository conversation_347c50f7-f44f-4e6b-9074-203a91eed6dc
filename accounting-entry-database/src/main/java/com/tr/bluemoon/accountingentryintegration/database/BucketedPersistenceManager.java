package com.tr.bluemoon.accountingentryintegration.database;

import com.thomsonreuters.database.LookupCache;
import com.thomsonreuters.database.models.Bucket;
import com.thomsonreuters.database.models.BucketLookup;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.EntityManager;
import javax.persistence.RollbackException;
import javax.persistence.TypedQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** Created by jdb on 1/19/16. */
public class BucketedPersistenceManager {

  private static final Logger LOGGER = LoggerFactory.getLogger(BucketedPersistenceManager.class);
  private static final int MAX_TRIES = 3;
  private static Map<String, BucketedPersistenceManager> INSTANCES = new HashMap<>();
  private final String bucketType;
  private final String persistenceUnit;

  // Map of datasource key to persistence manager
  private final Map<String, PersistenceManager> buckets = new HashMap<>();
  private final LookupCache cache;

  private BucketedPersistenceManager(final String bucketType, final String persistenceUnit) {
    this.bucketType = bucketType;
    this.persistenceUnit = persistenceUnit;
    this.cache = LookupCache.getCache(bucketType);
  }

  /**
   * Generate a BucketedPersistenceManager for a given type of bucket.
   *
   * @param bucketType Type of bucket
   * @param persistenceUnit Persistence unit defined in persistence.xml to be used with this
   *     persistence manager
   * @return BucketedPersistenceManager
   */
  public static BucketedPersistenceManager getInstance(
      final String bucketType, final String persistenceUnit) {
    return INSTANCES.computeIfAbsent(
        bucketType, k -> new BucketedPersistenceManager(bucketType, persistenceUnit));
  }

  /**
   * Generate or return an existing PersistenceManager of the correct bucket for a given identifier.
   *
   * @param id Bucket storage identifier (company ID, profile ID, etc.)
   * @param dataSourceKey key for the datasource
   * @return PersistenceManager for this bucket
   */
  public PersistenceManager getBucket(final String id, final String dataSourceKey) {
    LookupCache.BucketLite bucket = getCacheEntry(id, dataSourceKey, 1);

    return getPersistenceManager(bucket);
  }

  private LookupCache.BucketLite getCacheEntry(
      final String id, final String datasourceKey, int tryNumber) {
    LookupCache.BucketLite bucket = cache.lookup(id);
    if (bucket != null) {
      return bucket;
    }

    BucketLookup lookup = lookupBucket(id, bucketType, datasourceKey);
    if (lookup != null) {
      return cache.add(id, lookup.getBucket());
    }

    lookup = new BucketLookup(id);
    lookup.setBucket(Bucket.getNextBucket(bucketType));
    lookup.setDomainKey("test");
    if (id != null) {
      try {
        lookup.save();
      } catch (RollbackException ex) {
        if (tryNumber < MAX_TRIES) {
          return getCacheEntry(id, datasourceKey, ++tryNumber);
        }
        throw ex;
      }
    }

    return cache.add(id, lookup.getBucket());
  }

  private BucketLookup lookupBucket(
      final String id, final String bucketType, final String datasourceKey) {
    final EntityManager mgr =
        new BluemoonBucketPersistenceManager().createEntityManager(datasourceKey);
    try {
      final TypedQuery<BucketLookup> query =
          mgr.createQuery(
              "select b from BucketLookup b where b.id = :id and UPPER(b.status) = 'ACT' and "
                  + "b.bucket.bucketType = :bucketType and UPPER(b.bucket.status) = 'ACT' and b.bucket.pending = false",
              BucketLookup.class);
      query.setParameter("id", id);
      query.setParameter("bucketType", bucketType);
      final List<BucketLookup> results = query.getResultList();
      if (results != null && !results.isEmpty()) {
        if (results.size() > 1) {
          LOGGER.warn("Storage ID '{}' has {} buckets", id, results.size());
        }
        return results.get(0);
      }

      return null;
    } finally {
      mgr.close();
    }
  }

  private PersistenceManager getPersistenceManager(final LookupCache.BucketLite bucket) {
    String bucketKey = bucket.getBucketKey();
    PersistenceManager manager = buckets.get(bucket.getBucketKey());
    if (manager == null) {
      manager =
          PersistenceManager.createPersistenceManager(
              bucket.getDatasourceKey(), bucket.getBucketKey(), persistenceUnit);
      buckets.put(bucketKey, manager);
    }

    return manager;
  }

  /**
   * Get entity managers for all of the possible active buckets for the bucket type.
   *
   * @return Entity managers
   */
  public Collection<EntityManager> getAllEntityManagers() {
    List<EntityManager> managers = new ArrayList<>();
    List<Bucket> activeBuckets = Bucket.getActiveBuckets(bucketType);
    for (Bucket bucket : activeBuckets) {
      EntityManager manager =
          PersistenceManager.createPersistenceManager(
                  bucket.getDatasourceKey(), bucket.getBucketKey(), persistenceUnit)
              .createEntityManager();
      managers.add(manager);
    }
    return managers;
  }

  /**
   * Return a PersistenceManager for the first bucket available for the bucket type.
   *
   * @return PersistenceManager
   */
  public PersistenceManager getFirstBucket() {
    List<Bucket> activeBuckets = Bucket.getActiveNonDeprecatedBuckets(bucketType);
    if (activeBuckets.isEmpty()) {
      return null;
    }

    final Bucket bucket = activeBuckets.get(0);
    return PersistenceManager.createPersistenceManager(
        bucket.getDatasourceKey(), bucket.getBucketKey(), persistenceUnit);
  }

  /**
   * Run a JPA task on all buckets.
   *
   * @param task Task to run on all buckets of this type
   */
  public void runOnAllBuckets(final Task task) {
    Collection<EntityManager> managers = getAllEntityManagers();
    List<TaskThread> threads = new ArrayList<>(managers.size());
    // Create threads to run the task
    for (final EntityManager mgr : managers) {
      TaskThread thread = new TaskThread(task, mgr);
      thread.start();
      threads.add(thread);
    }

    // Wait for all of the threads to finish
    for (final TaskThread thread : threads) {
      try {
        thread.join();
        if (thread.getException() != null) {
          throw thread.getException();
        }
      } catch (InterruptedException e) {
        LOGGER.error(e.getMessage(), e);

        // Sonar does not like interruptions in threads being ignored. Recommended is to propagate
        // interruption.
        Thread.currentThread().interrupt();
      }
    }
  }

  /** Task to run on all buckets. */
  public interface Task {
    void performTask(final EntityManager mgr);
  }

  // Thread object on with to run the task
  private static class TaskThread extends Thread {
    private final Task task;
    private final EntityManager manager;
    private RuntimeException exception = null;

    public TaskThread(final Task task, final EntityManager manager) {
      this.task = task;
      this.manager = manager;
    }

    @Override
    public void run() {
      try {
        try {
          task.performTask(manager);
        } catch (RuntimeException e) {
          LOGGER.error(e.getMessage(), e);
          exception = e;
        } catch (Exception e) {
          LOGGER.error(e.getMessage(), e);
        }
      } finally {
        manager.close();
      }
    }

    public RuntimeException getException() {
      return exception;
    }
  }
}
