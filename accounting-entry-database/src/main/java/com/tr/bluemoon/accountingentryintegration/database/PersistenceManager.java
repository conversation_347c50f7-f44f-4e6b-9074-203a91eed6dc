package com.tr.bluemoon.accountingentryintegration.database;

import com.google.common.base.Strings;
import com.thomsonreuters.database.PropertyUtil;
import com.thomsonreuters.database.oracle.OracleDataSourceManager;
import com.thomsonreuters.database.postgresql.PostgresqlDataSourceManager;
import java.util.HashMap;
import java.util.Map;
import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.Persistence;
import org.eclipse.persistence.config.PersistenceUnitProperties;

/**
 * Connect to any PostgreSQL or Oracle database given a CMDB database connection, persistence.xml
 * persistence unit name, and optionally a database/schema name. Created by jdb on 12/20/15.
 */
public class PersistenceManager {

  // Map of persistence managers to avoid unnecessary object creation when possible
  private static Map<String, PersistenceManager> MANAGERS = new HashMap<>();
  private boolean isPostgres;
  // entity manager factory and thread local relevant to this persistence manager
  private EntityManagerFactory factory = null;
  private InheritableThreadLocal<EntityManager> entityManagerThreadLocal =
      new InheritableThreadLocal<>();

  private PersistenceManager(final String dataSource, final String persistenceUnit) {
    this(dataSource, null, persistenceUnit);
  }

  private PersistenceManager(
      final String dataSource, final String database, final String persistenceUnit) {
    final String dataSourceClassName =
        PropertyUtil.getProperty(dataSource, "jdbc.dataSourceClassName");
    if (dataSourceClassName == null) {
      throw new ExceptionInInitializerError(
          "DataSourceClassName: " + dataSource + " is not defined");
    }
    isPostgres =
        Strings.isNullOrEmpty(dataSourceClassName)
            ? false
            : dataSourceClassName.contains("postgres");

    final Map<String, Object> extraJpaProperties = new HashMap<>();

    if (isPostgres) {
      extraJpaProperties.put(
          PersistenceUnitProperties.NON_JTA_DATASOURCE,
          PostgresqlDataSourceManager.getDataSource(dataSource, database));
    } else {
      extraJpaProperties.put(
          PersistenceUnitProperties.NON_JTA_DATASOURCE,
          OracleDataSourceManager.getDataSource(dataSource, database));
      OracleDataSourceManager.addExtraConnectionProperties(
          dataSource, database, extraJpaProperties);
    }

    extraJpaProperties.put(
        PersistenceUnitProperties.ECLIPSELINK_PERSISTENCE_XML,
        PropertyUtil.getProperty(
            dataSource,
            "eclipselink.persistencexml",
            PersistenceUnitProperties.ECLIPSELINK_PERSISTENCE_XML_DEFAULT));

    factory = Persistence.createEntityManagerFactory(persistenceUnit, extraJpaProperties);
  }

  /**
   * Create a persistence manager for a given CMDB database connection and JPA persistence unit.
   * Database name is expected to be defined in the CMDB database properties.
   *
   * @param dataSource CMDB database connection name
   * @param persistenceUnit Persistence unit name in persistence.xml file
   * @return Valid PersistenceManager
   */
  public static PersistenceManager createPersistenceManager(
      final String dataSource, final String persistenceUnit) {
    final String key = generateKey(dataSource, null, persistenceUnit);
    return MANAGERS.computeIfAbsent(key, k -> new PersistenceManager(dataSource, persistenceUnit));
  }

  /**
   * Create a persistence manager for a given CMDB database connection and JPA persistence unit.
   *
   * @param dataSource CMDB database connection name
   * @param database Database to connect to
   * @param persistenceUnit Persistence unit name in persistence.xml file
   * @return Valid PersistenceManager
   */
  public static PersistenceManager createPersistenceManager(
      final String dataSource, final String database, final String persistenceUnit) {
    final String key = generateKey(dataSource, database, persistenceUnit);
    return MANAGERS.computeIfAbsent(
        key, k -> new PersistenceManager(dataSource, database, persistenceUnit));
  }

  private static String generateKey(
      final String dataSource, final String database, final String persistenceUnit) {
    return dataSource + "-" + database + "-" + persistenceUnit;
  }

  public EntityManagerFactory getFactory() {
    return factory;
  }

  public void close() {
    if (entityManagerThreadLocal.get() != null && entityManagerThreadLocal.get().isOpen()) {
      entityManagerThreadLocal.get().close();
    }
  }

  public EntityManager createEntityManager() {
    return factory.createEntityManager();
  }

  public EntityManager getEntityManager() {
    if (entityManagerThreadLocal.get() == null || !entityManagerThreadLocal.get().isOpen()) {
      entityManagerThreadLocal.set(factory.createEntityManager());
    }
    return entityManagerThreadLocal.get();
  }

  /**
   * Does this persistence manager return a PostgreSQL connection.
   *
   * @return true/false
   */
  public boolean isPostgresDatabase() {
    return isPostgres;
  }

  /**
   * Native SQL used to validate the database connection.
   *
   * @return Native SQL
   */
  public String getConnectionTestQuery() {
    return isPostgres ? "SELECT 1" : "SELECT 1 FROM DUAL";
  }

  /**
   * PostgreSQL uses true/false for boolean fields, Oracle uses 1/0 numeric field.
   *
   * @return false for PostgreSQL or 0 for Oracle
   */
  public Object getDefinitionOfFalse() {
    return isPostgres ? Boolean.FALSE : 0;
  }
}
