plugins {
    id 'org.springframework.boot' version "${springBootVersion}"
}

apply plugin: 'java-library'
apply plugin: 'io.spring.dependency-management'

bootJar {
    enabled = false
}

configurations {
    all {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }
}

dependencies {
    // Explicitly declaring snakeyaml v2.0
    implementation 'org.yaml:snakeyaml:2.0'
    implementation (project(":accounting-entry-database")){
        exclude (group: 'org.postgresql', module: 'postgresql')
    }

    api ('com.tr.bluemoon.bracct:bracct-rabbitmq:5.0.0-rc.2') {
        exclude group: 'org.glassfish.jersey.media', module: 'jersey-media-jaxb'
        exclude group: 'org.bouncycastle', module: 'bcprov-jdk15on'
    }
    implementation "org.bouncycastle:bcprov-jdk18on:1.80"
    implementation 'org.glassfish.jersey.media:jersey-media-jaxb:2.31'
    api 'com.tr.bluemoon.brtap:domain-awareness:1.0.0'
    implementation "com.tr.bluemoon.brtap:commons-configuration:${platformCommonsVersion}"
    implementation "com.tr.bluemoon.brtap:commons:${brtapCommonsVersion}"
    implementation "com.tr.bluemoon.brtap:commons-cryptography:${platformCommonsVersion}"
    api "com.tr.bluemoon.brtap:commons-utils:${platformCommonsVersion}"
    api "com.tr.bluemoon.brtap:commons-amq:${brtapCommonsVersion}"
    api "com.tr.bluemoon.brtap:commons-token:${platformCommonsVersion}"
    implementation 'commons-io:commons-io:2.19.0'
    implementation "org.springframework:spring-web:${springVersion}"

    api 'io.cloudevents:cloudevents-core:2.3.0'
    api('io.cloudevents:cloudevents-kafka:2.3.0') {
        exclude group: 'org.apache.kafka', module: 'kafka-clients'
    }
    implementation 'io.cloudevents:cloudevents-json-jackson:3.0.0'
    implementation "org.springframework.boot:spring-boot-starter-amqp:${springBootVersion}"
    implementation ("org.springframework.boot:spring-boot-starter-data-jpa:${springBootVersion}") {
        exclude group: 'org.postgresql', module: 'postgresql'
    }

    implementation("org.springframework.kafka:spring-kafka:2.9.12") {
        exclude(group: 'org.apache.kafka', module: 'kafka-clients')
    }
    implementation "org.apache.kafka:kafka-clients:3.8.1"
    api platform('software.amazon.awssdk:bom:2.29.52')
    api 'software.amazon.awssdk:s3'

    api ("com.tr.bluemoon.bracct:bracct-commons-jpa:${bracctCommonsJpaVersion}") {
        exclude group: 'org.postgresql', module: 'postgresql'
        exclude group: 'org.bouncycastle', module: 'bcprov-jdk15on'
    }
    implementation "org.postgresql:postgresql:${postgresqlVersion}"

    // DeltaSpike
    implementation "org.apache.deltaspike.modules:deltaspike-jpa-module-impl:${deltaSpikeVersion}"
    implementation "org.apache.deltaspike.modules:deltaspike-jpa-module-api:${deltaSpikeVersion}"

    // JPA implementation
    implementation ("org.eclipse.persistence:eclipselink:${eclipseLinkVersion}") {
        exclude group: 'org.eclipse.persistence', module: 'javax.persistence'
    }

    // Needed for JSON parsing.
    implementation "org.eclipse.persistence:org.eclipse.persistence.moxy:${eclipseLinkVersion}"

    testImplementation 'org.mockito:mockito-inline:4.0.0'
}
