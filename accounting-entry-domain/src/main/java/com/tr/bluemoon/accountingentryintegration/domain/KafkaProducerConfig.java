package com.tr.bluemoon.accountingentryintegration.domain;

import io.cloudevents.CloudEvent;
import io.cloudevents.kafka.CloudEventSerializer;
import java.util.HashMap;
import java.util.Map;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SslConfigs;
import org.apache.kafka.common.config.TopicConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.TopicBuilder;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

@Configuration
public class KafkaProducerConfig {

  public static final String DOCUMENT_TOPIC = "accountingentryintegration.fct.document.1";
  public static final String TOPIC_TTL = "*********";
  public static final String SEGMENT_ROLL_OUT = "********";

  /**
   * Create an instance of admin that delegates to an AdminClient to create topics defined in the
   * application context.
   *
   * @return kafka admin
   */
  @Bean
  public KafkaAdmin admin() {
    String bootStrapServers = System.getProperty("bm.kafka.bootstrap.servers", "localhost:9092");
    String trustStoreLocation = System.getProperty("bm.kafka.truststore", "");

    Map<String, Object> configs = new HashMap<>();
    configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootStrapServers);

    if (!trustStoreLocation.isEmpty()) {
      configs.put(SslConfigs.SSL_TRUSTSTORE_LOCATION_CONFIG, trustStoreLocation);
      configs.put("security.protocol", "SSL");
    }
    final var admin = new KafkaAdmin(configs);
    admin.setModifyTopicConfigs(true);

    return admin;
  }

  @Bean
  public ProducerFactory<String, CloudEvent> producerFactory(KafkaAdmin admin) {
    Map<String, Object> configs = new HashMap<>(admin.getConfigurationProperties());
    configs.put(ProducerConfig.ACKS_CONFIG, "all");
    configs.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
    configs.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, CloudEventSerializer.class);

    return new DefaultKafkaProducerFactory<>(configs);
  }

  @Bean
  public KafkaTemplate<String, CloudEvent> kafkaTemplate(ProducerFactory<String, CloudEvent> producerFactory) {
    return new KafkaTemplate<>(producerFactory);
  }

  @Bean
  public NewTopic documentTopic() {
    return TopicBuilder.name(DOCUMENT_TOPIC)
        .config(TopicConfig.RETENTION_MS_CONFIG, TOPIC_TTL)
        .config(TopicConfig.CLEANUP_POLICY_CONFIG, TopicConfig.CLEANUP_POLICY_DELETE)
        .config(TopicConfig.SEGMENT_MS_CONFIG, SEGMENT_ROLL_OUT)
        .partitions(
            Integer.parseInt(System.getProperty("bm.kafka.document.partitions", "8")))
        .build();
  }

  @Bean
  public NewTopic documentTopicDlt() {
    return TopicBuilder.name(DOCUMENT_TOPIC)
        .config(TopicConfig.RETENTION_MS_CONFIG, TOPIC_TTL)
        .config(TopicConfig.CLEANUP_POLICY_CONFIG, TopicConfig.CLEANUP_POLICY_DELETE)
        .config(TopicConfig.SEGMENT_MS_CONFIG, SEGMENT_ROLL_OUT)
        .partitions(1).build();
  }

}
