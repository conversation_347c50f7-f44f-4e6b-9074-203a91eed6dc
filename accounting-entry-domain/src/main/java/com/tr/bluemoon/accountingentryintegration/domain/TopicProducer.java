package com.tr.bluemoon.accountingentryintegration.domain;


import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitPostProcessor;
import com.tr.bluemoon.accountingentryintegration.domain.outboxevent.OutboxEvent;
import com.tr.bluemoon.accountingentryintegration.domain.outboxevent.OutboxEventService;
import com.tr.bluemoon.brtap.commons.security.ContextHolder;
import com.tr.bluemoon.commons.configuration.BlueMoonDomain;
import io.cloudevents.CloudEvent;
import io.cloudevents.core.builder.CloudEventBuilder;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.OffsetDateTime;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.lang.NonNull;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

public abstract class TopicProducer {

  private static final Logger log = LoggerFactory.getLogger(TopicProducer.class);

  private final ObjectMapper mapper;
  private final OutboxEventService outboxEventService;
  private final KafkaTemplate<String, CloudEvent> kafkaTemplate;

  public TopicProducer(ObjectMapper objectMapper, OutboxEventService outboxEventService,
      KafkaTemplate<String, CloudEvent> kafkaTemplate) {
    this.mapper = objectMapper;
    this.outboxEventService = outboxEventService;
    this.kafkaTemplate = kafkaTemplate;
  }

  /**
   * An entity event will be converted into to CloudEvent and published to kafka. The responsibility
   * of sending message to the topic is on the kafka connector, here we save the event to an outbox
   * table, and that message will be routed using OutboxEventRouter.
   *
   * @param entityEvent an event with id
   * @param eventType   string describing cloud event type <a
   *                    href="https://github.com/cloudevents/spec/blob/v1.0/spec.md#type">See</a>
   * @throws IllegalArgumentException when id is not present
   */
  public void publishEntityToOutbox(EntityEvent entityEvent, String eventType, String topic) {
    if (entityEvent.getId() == null) {
      throw new IllegalArgumentException("Could not publish event without id");
    }

    ContextHolder contextHolder = RabbitPostProcessor.getContext();

    try {
      outboxEventService.save(OutboxEvent.builder()
          .source(BlueMoonDomain.getCurrent())
          .chainId(UUID.randomUUID()).companyId(contextHolder.getCompanyId())
          .contactId(contextHolder.getContactId()).eventType(eventType).eventId(entityEvent.getId())
          .topic(topic).partitionKey(entityEvent.getId().toString())
          .payload(new String(mapper.writeValueAsBytes(entityEvent), StandardCharsets.UTF_8))
          .createdBy(contextHolder.getContactId()).build());
    } catch (JsonProcessingException e) {
      log.error("Invalid message");
    }

  }

  /**
   * An entity event will be converted into to CloudEvent and published to kafka.
   *
   * @param entityEvent an event with id
   * @param eventType   string describing cloud event type <a
   *                    href="https://github.com/cloudevents/spec/blob/v1.0/spec.md#type">See</a>
   * @throws IllegalArgumentException when id is not present
   */
  public void publishEntityToKafka(EntityEvent entityEvent, String eventType, String topic) {
    if (entityEvent.getId() == null) {
      throw new IllegalArgumentException("Could not publish event without id");
    }

    ContextHolder contextHolder = RabbitPostProcessor.getContext();

    try {
      mapper.setVisibility(PropertyAccessor.FIELD, Visibility.ANY);

      CloudEvent build = CloudEventBuilder.v1().withId(UUID.randomUUID().toString())
          .withTime(OffsetDateTime.now())
          .withSource(URI.create(contextHolder.getBlueMoonDomain().getDomain()))
          .withExtension("comtrbluemoonchainid", UUID.randomUUID().toString())
          .withExtension("comtrbluemooncompanyid", contextHolder.getCompanyId().toString())
          .withExtension("comtrbluemooncontactid", contextHolder.getContactId().toString())
          .withType(eventType).withSubject(entityEvent.getId().toString())
          .withDataContentType("application/json").withData(mapper.writeValueAsBytes(entityEvent))
          .build();
      publish(topic, entityEvent.getId().toString(), build);
    } catch (JsonProcessingException e) {
      log.error("Invalid message", e);
    }
  }

  private void publish(String topic, String partitionKey, CloudEvent event) {
    ListenableFuture<SendResult<String, CloudEvent>> future =
        kafkaTemplate.send(topic, partitionKey, event);

    future.addCallback(new ListenableFutureCallback<>() {

      @Override
      public void onSuccess(@NonNull SendResult<String, CloudEvent> result) {
        log.debug("Event sent to the topic {} and partition {}", result.getRecordMetadata().topic(),
            result.getRecordMetadata().partition());
      }

      @Override
      public void onFailure(Throwable ex) {
        log.error("Unable to send message to topic [ " + topic + "] due to : ", ex);
      }
    });
  }
}
