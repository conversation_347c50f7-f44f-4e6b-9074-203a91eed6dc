package com.tr.bluemoon.accountingentryintegration.domain.amqp;

import io.cloudevents.CloudEvent;
import io.cloudevents.core.builder.CloudEventBuilder;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.springframework.amqp.AmqpRejectAndDontRequeueException;
import org.springframework.amqp.ImmediateAcknowledgeAmqpException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractConsumer {

  protected static final String RETRY_HEADER = "x-death";
  private static final Pattern XDEATH_COUNT_PATTERN = Pattern.compile("count=(\\d+)(,|})");
  private static final int DEFAULT_MAX_RETRIES = 2;

  @Autowired RabbitTemplate rabbitTemplate;

  protected int maxRetries(String eventType) {
    return DEFAULT_MAX_RETRIES;
  }

  /**
   * <PERSON>le failed executions. If it is not fatal it will move message to a retry queue. If reached
   * MAX_RETRIES then republish message to a DLQ.
   *
   * @param event event to be publish to dead letter if MAX_RETRIES is exhausted.
   * @param death death counting
   * @param dlx dead letter exchange to publish message to
   * @param cause to print stacktrace to CloudEvent when it gets published to dead letter
   */
  protected void handleRetry(CloudEvent event, Map<?, ?> death, String dlx, Exception cause) {
    final int retryTimes = getRetryTimes(death);

    if (retryTimes >= maxRetries(event.getType())) {
      final var stringWriter = new StringWriter();
      final var printWriter = new PrintWriter(stringWriter);
      cause.printStackTrace(printWriter);

      final var dlqEvent =
          CloudEventBuilder.from(event)
              .withExtension("stacktrace", stringWriter.toString())
              .build();

      rabbitTemplate.convertAndSend(dlx, dlqEvent.getType(), dlqEvent);
      throw new ImmediateAcknowledgeAmqpException("Max retries reached.");
    }

    // Retry one more time
    throw new AmqpRejectAndDontRequeueException("Attempt " + (retryTimes + 1) + ". Retrying...");
  }

  protected final int getRetryTimes(Map<?, ?> death) {
    return Optional.ofNullable(death).map(Objects::toString).map(this::parseXdeathCount).orElse(0);
  }

  private Integer parseXdeathCount(String xdeath) {
    Matcher matcher = XDEATH_COUNT_PATTERN.matcher(xdeath);
    if (matcher.find()) {
      try {
        return Integer.parseInt(matcher.group(1));
      } catch (NumberFormatException e) {
        return 0;
      }
    } else {
      return 0;
    }
  }
}
