package com.tr.bluemoon.accountingentryintegration.domain.amqp;

import javax.enterprise.inject.Alternative;
import lombok.Getter;

@Alternative
@Getter
public class BindingDefinition {

  private final String exchange;
  private final boolean destinationQueue;
  private final String destination;
  private final String routingKey;

  // Enable CDI proxying
  public BindingDefinition() {
    exchange = null;
    destinationQueue = false;
    destination = null;
    routingKey = null;
  }

  public BindingDefinition(
      String exchange, boolean destinationQueue, String destination, String routingKey) {
    this.exchange = exchange;
    this.destinationQueue = destinationQueue;
    this.destination = destination;
    this.routingKey = routingKey;
  }

  public BindingDefinition(
      ExchangeDefinition exchange, ExchangeDefinition destination, String routingKey) {
    this.exchange = exchange.getName();
    this.destinationQueue = false;
    this.destination = destination.getName();
    this.routingKey = routingKey;
  }

  public BindingDefinition(
      ExchangeDefinition exchange, QueueDefinition destination, String routingKey) {
    this.exchange = exchange.getName();
    this.destinationQueue = true;
    this.destination = destination.getName();
    this.routingKey = routingKey;
  }
}
