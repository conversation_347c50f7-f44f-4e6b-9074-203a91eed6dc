package com.tr.bluemoon.accountingentryintegration.domain.amqp;

import io.cloudevents.CloudEvent;
import io.cloudevents.core.provider.EventFormatProvider;
import io.cloudevents.jackson.JsonFormat;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.support.converter.MessageConversionException;
import org.springframework.amqp.support.converter.MessageConverter;

public class CloudEventMessageConverter implements MessageConverter {

  @SuppressWarnings("java:S2583")
  public static byte[] convertToBytes(CloudEvent event) {
    var eventFormat = EventFormatProvider.getInstance().resolveFormat(JsonFormat.CONTENT_TYPE);

    if (eventFormat != null) {
      return eventFormat.serialize(event);
    }

    return new byte[] {};
  }

  @SuppressWarnings("java:S2583")
  public static CloudEvent convertFromBytes(byte[] event) {
    var eventFormat = EventFormatProvider.getInstance().resolveFormat(JsonFormat.CONTENT_TYPE);

    if (eventFormat != null) {
      return eventFormat.deserialize(event);
    }

    return null;
  }

  @Override
  public Message toMessage(Object object, MessageProperties messageProperties)
      throws MessageConversionException {
    if (object instanceof CloudEvent) {
      final byte[] bytes = convertToBytes((CloudEvent) object);
      return new Message(bytes, messageProperties);
    }

    throw new MessageConversionException("Unsuported object type. Object must be a CloudEvent.");
  }

  @SuppressWarnings("java:S2583")
  @Override
  public Object fromMessage(Message message) throws MessageConversionException {
    var eventFormat = EventFormatProvider.getInstance().resolveFormat(JsonFormat.CONTENT_TYPE);

    // Event format can be null because resolveFormat can evaluate to null
    if (eventFormat == null) {
      throw new IllegalStateException("EventFormatProvider resolves to a null EventFormat.");
    }

    return eventFormat.deserialize(message.getBody());
  }
}
