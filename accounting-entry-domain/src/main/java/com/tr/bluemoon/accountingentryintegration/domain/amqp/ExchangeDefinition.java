package com.tr.bluemoon.accountingentryintegration.domain.amqp;

import javax.enterprise.inject.Alternative;
import lombok.experimental.Delegate;
import org.springframework.amqp.core.TopicExchange;

@Alternative
public class ExchangeDefinition {

  @Delegate(types = TopicExchange.class)
  private final TopicExchange delegate;

  // Enable CDI proxying
  public ExchangeDefinition() {
    delegate = new TopicExchange("");
  }

  public ExchangeDefinition(String name, boolean durable, boolean autoDelete) {
    delegate = new TopicExchange(name, durable, autoDelete);
  }
}
