package com.tr.bluemoon.accountingentryintegration.domain.amqp;

import javax.enterprise.inject.Alternative;
import lombok.experimental.Delegate;
import org.springframework.amqp.core.Queue;

@Alternative
public class QueueDefinition {

  @Delegate(types = Queue.class)
  private final org.springframework.amqp.core.Queue delegate;

  // Enable CDI proxying
  public QueueDefinition() {
    delegate = new Queue("");
  }

  public QueueDefinition(String name, boolean durable) {
    delegate = new Queue(name, durable);
  }
}
