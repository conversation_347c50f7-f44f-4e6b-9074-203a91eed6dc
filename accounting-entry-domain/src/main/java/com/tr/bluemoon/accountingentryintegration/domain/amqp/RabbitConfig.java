package com.tr.bluemoon.accountingentryintegration.domain.amqp;

import static com.tr.bluemoon.accountingentryintegration.domain.document.DocumentMissedConfig.DOCUMENT_MISSED_EVENT;

import com.tr.bluemoon.commons.cryptography.security.Encryption;
import com.tr.bluemoon.commons.cryptography.security.EncryptionException;
import java.util.List;
import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.inject.Produces;
import javax.inject.Named;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Binding.DestinationType;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@EnableRabbit
@Configuration
public class RabbitConfig {

  // Exchanges
  public static final String ACCOUNTING_ENTRY_INTEGRATION_EXCHANGE =
      "com.tr.bluemoon.exchange.brtap.accountingentryintegration.pubsub";
  public static final String ACCOUNTING_ENTRY_INTEGRATION_RETRY_EXCHANGE =
      "com.tr.bluemoon.exchange.brtap.accountingentryintegration.retry";
  public static final String ACCOUNTING_ENTRY_INTEGRATION_DEAD_LETTER_EXCHANGE =
      "com.tr.bluemoon.exchange.brtap.accountingentryintegration.deadletter";

  public static final String DOCUMENT_AVAILABLE_EVENT =
      "com.tr.bluemoon.event.brtap.accountingentryintegration.document.v1.available";

  // External exchanges
  private static final String SOCKET_SERVICES_EXCHANGE = "com.tr.bluemoon.exchange.common.pubsub";

  @Value("${RabbitMQHostName}")
  private String rmqHost;

  @Value("${RabbitMQPort}")
  private Integer rmqPort;

  @Value("${RabbitMQVirtualHostName}")
  private String rmqVirtualHost;

  @Value("${RabbitMQUserName}")
  private String rmqUsername;

  // Declare exchanges -----------------------------------------------------------------------------
  @ApplicationScoped
  @Produces
  @Named("accountingEntryIntegrationExchange")
  @Bean
  public ExchangeDefinition accountingEntryIntegrationExchange() {
    return new ExchangeDefinition(ACCOUNTING_ENTRY_INTEGRATION_EXCHANGE, true, false);
  }

  @ApplicationScoped
  @Produces
  @Named("accountingEntryIntegrationRetryExchange")
  @Bean
  public ExchangeDefinition accountingEntryIntegrationRetryExchange() {
    return new ExchangeDefinition(ACCOUNTING_ENTRY_INTEGRATION_RETRY_EXCHANGE, true, false);
  }

  @ApplicationScoped
  @Produces
  @Named("accountingEntryIntegrationDeadLetterExchange")
  @Bean
  public ExchangeDefinition accountingEntryIntegrationDeadLetterExchange() {
    return new ExchangeDefinition(ACCOUNTING_ENTRY_INTEGRATION_DEAD_LETTER_EXCHANGE, true, false);
  }

  // This is not a bean intentionally, because it is responsibility of socket services to provision
  // the exchange
  // This is here just for reference, to be able to binding Accounting Entry integration exchange to
  // socket services exchange
  private ExchangeDefinition socketServicesExchange() {
    return new ExchangeDefinition(SOCKET_SERVICES_EXCHANGE, true, false);
  }

  @ApplicationScoped
  @Produces
  @Named("accountingEntryIntegrationToSocketServicesBinding")
  @Bean
  public BindingDefinition accountingEntryIntegrationToSocketServicesBinding() {
    return new BindingDefinition(
        accountingEntryIntegrationExchange(), socketServicesExchange(), DOCUMENT_AVAILABLE_EVENT);
  }

  @ApplicationScoped
  @Produces
  @Named("socketServicesToAccountingEntryIntegrationBinding")
  @Bean
  public BindingDefinition socketServicesToAccountingEntryIntegrationBinding() {
    return new BindingDefinition(
        socketServicesExchange(), accountingEntryIntegrationExchange(), DOCUMENT_MISSED_EVENT);
  }

  @ApplicationScoped
  @Produces
  @Bean
  public ConnectionFactory connectionFactory() throws EncryptionException {
    final CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
    connectionFactory.setHost(rmqHost);
    connectionFactory.setPort(rmqPort);
    connectionFactory.setVirtualHost(rmqVirtualHost);
    connectionFactory.setUsername(rmqUsername);
    connectionFactory.setPassword(Encryption.getCMDBProperty("RabbitMQPassword"));
    return connectionFactory;
  }

  @Bean
  public RabbitTemplate rabbitTemplate(final ConnectionFactory connectionFactory) {
    final var rabbitTemplate = new RabbitTemplate(connectionFactory);
    rabbitTemplate.setMessageConverter(new CloudEventMessageConverter());
    return rabbitTemplate;
  }

  @Bean
  public AmqpAdmin amqpAdmin(
      RabbitTemplate rabbitTemplate,
      List<ExchangeDefinition> exchanges,
      List<QueueDefinition> queues,
      List<BindingDefinition> bindings) {
    final var admin = createRabbitAdmin(rabbitTemplate);

    exchanges.stream().map(this::mapToExchange).forEach(admin::declareExchange);

    queues.stream().map(this::mapToQueue).forEach(admin::declareQueue);

    bindings.stream().map(this::mapToBinding).forEach(admin::declareBinding);

    return admin;
  }

  // It is to possibly mock on tests
  protected AmqpAdmin createRabbitAdmin(RabbitTemplate rabbitTemplate) {
    return new RabbitAdmin(rabbitTemplate);
  }

  private Exchange mapToExchange(ExchangeDefinition exchangeDefinition) {
    return new TopicExchange(
        exchangeDefinition.getName(),
        exchangeDefinition.isDurable(),
        exchangeDefinition.isAutoDelete(),
        exchangeDefinition.getArguments());
  }

  private Queue mapToQueue(QueueDefinition queueDefinition) {
    return new Queue(
        queueDefinition.getName(),
        queueDefinition.isDurable(),
        queueDefinition.isExclusive(),
        queueDefinition.isAutoDelete(),
        queueDefinition.getArguments());
  }

  private Binding mapToBinding(BindingDefinition bindingDefinition) {
    return new Binding(
        bindingDefinition.getDestination(),
        bindingDefinition.isDestinationQueue() ? DestinationType.QUEUE : DestinationType.EXCHANGE,
        bindingDefinition.getExchange(),
        bindingDefinition.getRoutingKey(),
        null);
  }
}
