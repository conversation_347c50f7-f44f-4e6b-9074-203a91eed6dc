package com.tr.bluemoon.accountingentryintegration.domain.amqp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.rabbitmq.client.AMQP;
import com.tr.bluemoon.bracct.commons.UserInfo;
import com.tr.bluemoon.bracct.rabbitmq.Exchange;
import com.tr.bluemoon.bracct.rabbitmq.MessageProducer;
import com.tr.bluemoon.brtap.commons.security.ContextHolder;
import com.tr.bluemoon.commons.configuration.BlueMoonDomain;
import io.cloudevents.CloudEvent;
import io.cloudevents.core.builder.CloudEventBuilder;
import java.io.IOException;
import java.net.URI;
import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeoutException;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.ws.rs.InternalServerErrorException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@ApplicationScoped
@Component
public class RabbitMQEnqueuer {

  private static final Logger LOGGER = LoggerFactory.getLogger(RabbitMQEnqueuer.class);
  private static final int PERSISTENT_MESSAGE = 2;

  // Gets injected by CDI. DON`T USE for internal application because it is null.
  private final RabbitMQServer server;
  // Gets injected by spring. DON`T USE for external application because it is null.
  @Autowired RabbitTemplate rabbitTemplate;
  private ObjectMapper objectMapper;
  private MessageProducer producer;

  // CDI requires ApplicationScoped to have default constructor
  public RabbitMQEnqueuer() {
    server = null;
  }

  @Inject
  @Autowired(required = false)
  public RabbitMQEnqueuer(RabbitMQServer server) {
    this.server = server;
  }

  /**
   * Publish messages to rabbit MQ when in http context, because it has dependency with UserInfo
   * which is request scoped.
   *
   * <p>General use is for external application, because it is a JAXRS application and initiates
   * UserInfo in the request.
   *
   * @param payload rabbitMQ event to be published
   * @param routingKey routing key
   * @param exchange exchange to delivery message to
   * @param userInfo request scoped user information
   */
  public void publishMessage(
      RabbitMQEvent payload, String routingKey, String exchange, UserInfo userInfo) {
    final CloudEvent event =
        buildCloudEvent(
            payload,
            routingKey,
            userInfo.getCompanyId().toString(),
            userInfo.getContactId().toString(),
            BlueMoonDomain.getExternal());

    final var msgProducer = getMessageProducer(exchange);
    final byte[] bytes = CloudEventMessageConverter.convertToBytes(event);
    final var properties = getProperties();
    final Map<String, Object> headers = getHeaders();

    try {
      msgProducer.produce(bytes, routingKey, properties, headers);
    } catch (IOException e) {
      LOGGER.error("Failed to produce hello world message.", e);
      throw new InternalServerErrorException(e);
    } catch (TimeoutException e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * Publish messages to rabbit MQ when in AMQP context, because it has dependency with
   * ContextHolder.
   *
   * <p>General use is for internal application, because it is a spring application and initiates
   * ContextHolder on RabbitPostProcessor for every new message that is consume from a queue.
   *
   * @param payload rabbitMQ event to be published
   * @param routingKey routing key
   * @param exchange exchange to delivery message to
   * @param contextHolder information of the context provided by RabbitPostProcessor
   */
  public void publishMessage(
      RabbitMQEvent payload, String routingKey, String exchange, ContextHolder contextHolder) {
    final CloudEvent event =
        buildCloudEvent(
            payload,
            routingKey,
            contextHolder.getCompanyId().toString(),
            contextHolder.getContactId().toString(),
            contextHolder.getBlueMoonDomain().getDomain());

    rabbitTemplate.convertAndSend(exchange, event.getType(), event);
  }

  private CloudEvent buildCloudEvent(
      RabbitMQEvent payload, String routingKey, String companyId, String contactId, String domain) {
    try {
      return CloudEventBuilder.v1()
          .withId(UUID.randomUUID().toString())
          .withTime(OffsetDateTime.now())
          .withSource(URI.create(domain))
          .withExtension("comtrbluemooncompanyid", companyId)
          .withExtension("comtrbluemooncontactid", contactId)
          .withType(routingKey)
          .withSubject(payload.getId() == null ? companyId : payload.getId().toString())
          .withDataContentType("application/json")
          .withData(getMapper().writeValueAsBytes(payload))
          .build();
    } catch (JsonProcessingException e) {
      LOGGER.error("Failed to serialize message.", e);
      throw new InternalServerErrorException(e);
    }
  }

  private MessageProducer getMessageProducer(String exchangeName) {
    if (producer == null) {
      Exchange exchange = server.getExchange(exchangeName);
      producer = exchange.getMessageProducer(false);
    }
    return producer;
  }

  public AMQP.BasicProperties getProperties() {
    return new AMQP.BasicProperties().builder().deliveryMode(PERSISTENT_MESSAGE).build();
  }

  public Map<String, Object> getHeaders() {
    return new HashMap<>();
  }

  public ObjectMapper getMapper() {
    if (objectMapper == null) {
      objectMapper =
          JsonMapper.builder()
              .addModule(new JavaTimeModule())
              .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
              .serializationInclusion(JsonInclude.Include.NON_NULL)
              .build();
    }

    return objectMapper;
  }
}
