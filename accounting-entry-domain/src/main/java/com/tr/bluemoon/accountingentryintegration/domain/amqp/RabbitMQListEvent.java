package com.tr.bluemoon.accountingentryintegration.domain.amqp;

import java.util.List;
import java.util.UUID;
import lombok.EqualsAndHashCode;

/**
 * A list for many rabbit events that can be published using RabbitMQEnqueuer.
 *
 * @param <T> Event that are subtypes of RabbitMQEvent
 */
@EqualsAndHashCode
public class RabbitMQListEvent<T extends RabbitMQEvent> implements RabbitMQEvent {

  private final boolean isPackage;
  private final List<T> items;
  private final boolean hasMore;

  public RabbitMQListEvent(List<T> items, boolean isPackage, boolean hasMore) {
    this.items = items;
    this.isPackage = isPackage;
    this.hasMore = hasMore;
  }

  /**
   * No need for id because it is just a package/container for N-rabbitMQEvents which one with its
   * own id. RabbitMQEnqueuer will consider companyId as the subject of the event.
   *
   * @return null so that does not get serialized to json.
   */
  @Override
  public UUID getId() {
    return null;
  }

  public boolean isPackage() {
    return isPackage;
  }

  public boolean isHasMore() {
    return hasMore;
  }

  public List<T> getItems() {
    return items;
  }
}
