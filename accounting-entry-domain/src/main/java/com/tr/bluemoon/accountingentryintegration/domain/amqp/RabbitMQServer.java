package com.tr.bluemoon.accountingentryintegration.domain.amqp;

import com.tr.bluemoon.bracct.rabbitmq.Server;
import com.tr.bluemoon.commons.cryptography.security.Encryption;
import com.tr.bluemoon.commons.cryptography.security.EncryptionException;
import com.tr.bluemoon.commons.utils.LookupUtil;
import javax.enterprise.context.ApplicationScoped;

/**
 * Produces an instance of a RabbitMQ server with application scope.
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class RabbitMQServer extends Server {

  public static final String DEFAULT_PORT = "5672";
  private static final int DEFAULT_AMOUNT_THREADS = 1;

  public RabbitMQServer() throws EncryptionException {
    super(
        LookupUtil.get("RabbitMQHostName"),
        Integer.parseInt(LookupUtil.get("RabbitMQPort", DEFAULT_PORT)),
        LookupUtil.get("RabbitMQUserName"),
        Encryption.getCMDBProperty("Rabbit<PERSON>Q<PERSON>ass<PERSON>", null),
        LookupUtil.get("RabbitMQVirtualHostName"),
        DEFAULT_AMOUNT_THREADS);
  }
}
