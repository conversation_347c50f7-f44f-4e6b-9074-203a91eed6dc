package com.tr.bluemoon.accountingentryintegration.domain.amqp;

import com.tr.bluemoon.brtap.commons.amq.ContextHolderUtil;
import com.tr.bluemoon.brtap.commons.amq.LogKey;
import com.tr.bluemoon.brtap.commons.domainawareness.RoutingPaths;
import com.tr.bluemoon.brtap.commons.security.ContextHolder;
import io.cloudevents.CloudEvent;
import io.cloudevents.core.format.EventDeserializationException;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessagePostProcessor;

public class RabbitPostProcessor implements MessagePostProcessor {

  private static final ThreadLocal<ContextHolder> CONTEXT_HOLDER = new ThreadLocal<>();
  private static final Logger LOGGER = LoggerFactory.getLogger(RabbitPostProcessor.class);

  private final CloudEventMessageConverter converter;
  private final RoutingPaths routingPaths;

  public RabbitPostProcessor(CloudEventMessageConverter converter, RoutingPaths routingPaths) {
    this.converter = converter;
    this.routingPaths = routingPaths;
  }

  public static ContextHolder getContextOrDefault(ContextHolder def) {
    if (CONTEXT_HOLDER.get() == null) {
      return def;
    }

    return CONTEXT_HOLDER.get();
  }

  public static ContextHolder getContext() {
    if (CONTEXT_HOLDER.get() == null) {
      throw new IllegalStateException("Context is not registered.");
    }

    return CONTEXT_HOLDER.get();
  }

  public static void cleanContext() {
    CONTEXT_HOLDER.remove();
  }

  @Override
  public Message postProcessMessage(Message message) throws AmqpException {
    final CloudEvent cloudEvent;
    try {
      cleanContext();
      MDC.clear();

      cloudEvent = (CloudEvent) converter.fromMessage(message);

      // Assign context
      final var context = ContextHolderUtil.fromCloudEvent(cloudEvent, routingPaths);
      CONTEXT_HOLDER.set(context);

      feedMDC(cloudEvent);

      return message;
    } catch (EventDeserializationException ex) {
      LOGGER.error("Error to deserialize message.", ex);

      return null; // Ack message, nothing else can be done
    } catch (Exception ex) {
      LOGGER.error("Error in pre-processing message.", ex);

      // Let spring delivery this message, so the application can decide what to do
      // It probably will fail next, but it can be properly treated later
      return message;
    }
  }

  private void feedMDC(CloudEvent event) {
    final UUID callId = UUID.randomUUID();
    MDC.clear();

    MDC.put(LogKey.CALL_ID.toString(), callId.toString());

    // chain-id
    final String chainId =
        Optional.ofNullable(event.getExtension("comtrbluemoonchainid"))
            .map(Objects::toString)
            .orElse(callId.toString());
    MDC.put(LogKey.CHAIN_ID.toString(), chainId);

    // company-id
    Optional.ofNullable(event.getExtension("comtrbluemooncompanyid"))
        .ifPresent(companyId -> MDC.put(LogKey.COMPANY.toString(), companyId.toString()));

    // user-id
    Optional.ofNullable(event.getExtension("comtrbluemoonuserid"))
        .ifPresent(userId -> MDC.put(LogKey.USER_ID.toString(), userId.toString()));
  }
}
