package com.tr.bluemoon.accountingentryintegration.domain.amqp;

import com.tr.bluemoon.brtap.commons.domainawareness.RoutingPaths;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RoutingPathsConfig {

  public static final String BR_ACCOUNTING_ENTRY_INTEGRATION_SERVICES =
      "BRAccountingEntryIntegrationServices";

  /*
   * For AMQ process, or any other that is not HTTP context tied we want to specify the paths
   * to the services
   */
  @Bean
  public RoutingPaths routingPaths() {
    return new RoutingPaths()
        .put(BR_ACCOUNTING_ENTRY_INTEGRATION_SERVICES, false, "/api/br-accounting-entry");
  }
}
