package com.tr.bluemoon.accountingentryintegration.domain.aws;

import com.tr.bluemoon.brtap.commons.exception.BusinessException;
import com.tr.bluemoon.commons.utils.LookupUtil;
import java.net.URI;
import java.net.URISyntaxException;
import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.inject.Produces;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;

@ApplicationScoped
@Configuration
public class AwsConfig {

  private static final String S3_REGION = "br.s3.region";
  private static final String S3_ENDPOINT_OVERRIDE = "br.s3.endpointOverride";

  @Bean
  @Produces
  @ApplicationScoped
  public S3Client buildS3Client() {
    final var s3Region = LookupUtil.get(S3_REGION);
    final var s3EndpointOverride = LookupUtil.get(S3_ENDPOINT_OVERRIDE);

    try {
      S3ClientBuilder builder = S3Client.builder();

      if (StringUtils.hasText(s3Region)) {
        builder = builder.region(Region.of(s3Region));
      }

      if (StringUtils.hasText(s3EndpointOverride)) {
        builder = builder.endpointOverride(new URI(s3EndpointOverride));
      }

      return builder.build();
    } catch (URISyntaxException ex) {
      throw new BusinessException("Cannot produce S3 Client", ex);
    }
  }

  @Bean
  @Produces
  @ApplicationScoped
  public S3Presigner s3Presigner() {
    final var s3Region = LookupUtil.get(S3_REGION);
    final var s3EndpointOverride = LookupUtil.get(S3_ENDPOINT_OVERRIDE);

    try {
      S3Presigner.Builder builder = S3Presigner.builder();

      if (StringUtils.hasText(s3Region)) {
        builder = builder.region(Region.of(s3Region));
      }

      if (StringUtils.hasText(s3EndpointOverride)) {
        builder = builder.endpointOverride(new URI(s3EndpointOverride));
      }

      return builder.build();
    } catch (URISyntaxException ex) {
      throw new RuntimeException("Cannot produce S3 Presigner", ex);
    }
  }
}
