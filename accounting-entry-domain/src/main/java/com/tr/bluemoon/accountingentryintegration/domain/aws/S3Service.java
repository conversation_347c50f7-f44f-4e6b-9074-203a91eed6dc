package com.tr.bluemoon.accountingentryintegration.domain.aws;

import com.tr.bluemoon.commons.utils.LookupUtil;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.UUID;
import javax.inject.Inject;
import javax.ws.rs.InternalServerErrorException;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.core.exception.SdkException;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.CopyObjectRequest;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.Tag;
import software.amazon.awssdk.services.s3.model.Tagging;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;

@Component
public class S3Service {

  private static final Logger LOGGER = LoggerFactory.getLogger(S3Service.class);

  private static final String S3_BUCKET_KEY = "br.s3.bucket";
  private static final String CI_BUCKET = "a205451-br-services-ci-us-east-1";
  private static final String DAYS_TO_EXPIRE = "DaysToExpire";
  private static final String RETENTION_DAYS = "60";

  @Inject @Autowired S3Client s3Client;

  @Inject @Autowired S3Presigner s3Presigner;

  public String saveXml(
      InputStream xmlStream, String folder, UUID clientId, UUID companyId, String domain) {
    final String key = createKey(folder, clientId, companyId, domain);

    try {
      final String bucket = LookupUtil.get(S3_BUCKET_KEY, CI_BUCKET);

      s3Client.putObject(
          PutObjectRequest.builder()
              .bucket(bucket)
              .key(key)
              .tagging(
                  Tagging.builder()
                      .tagSet(Tag.builder().key(DAYS_TO_EXPIRE).value(RETENTION_DAYS).build())
                      .build())
              .build(),
          RequestBody.fromBytes(IOUtils.toByteArray(xmlStream)));

      return key;
    } catch (IOException | SdkException e) {
      LOGGER.error("Failed to save file to S3", e);
      throw new InternalServerErrorException("Failed to persist file.", e);
    }
  }

  protected UUID randomUuid() {
    return UUID.randomUUID();
  }

  public InputStream getXmlContentInputStream(String key) throws IOException {
    final String bucket = LookupUtil.get(S3_BUCKET_KEY, CI_BUCKET);

    return s3Client.getObject(GetObjectRequest.builder().bucket(bucket).key(key).build());
  }

  public String getPreSignedDownloadUrl(String key, Duration duration) {
    final String bucket = LookupUtil.get(S3_BUCKET_KEY, CI_BUCKET);
    final GetObjectRequest getObjectRequest =
        GetObjectRequest.builder().bucket(bucket).key(key).build();
    final GetObjectPresignRequest getObjectPresignRequest =
        GetObjectPresignRequest.builder()
            .signatureDuration(duration)
            .getObjectRequest(getObjectRequest)
            .build();
    final PresignedGetObjectRequest presignedGetObjectRequest =
        s3Presigner.presignGetObject(getObjectPresignRequest);
    return presignedGetObjectRequest.url().toString();
  }

  public void deleteXml(String key) {
    final String bucket = LookupUtil.get(S3_BUCKET_KEY, CI_BUCKET);

    s3Client.deleteObject(DeleteObjectRequest.builder().bucket(bucket).key(key).build());
  }

  public String copyXml(
      String originKey, String folder, UUID clientId, UUID companyId, String domain) {
    final String bucket = LookupUtil.get(S3_BUCKET_KEY, CI_BUCKET);
    final String destinationKey = createKey(folder, clientId, companyId, domain);

    final var copyObjRequest =
        CopyObjectRequest.builder()
            .copySource(bucket + "/" + originKey)
            .destinationBucket(bucket)
            .destinationKey(destinationKey)
            .build();

    s3Client.copyObject(copyObjRequest);

    return destinationKey;
  }

  private String createKey(String folder, UUID clientId, UUID companyId, String domain) {
    // domain/<companyId>/<category>/<clientId>/file_name.xml
    return domain
        + "/"
        + companyId
        + "/api/accountingentries/"
        + folder
        + "/"
        + clientId
        + "/"
        + randomUuid()
        + ".xml";
  }
}
