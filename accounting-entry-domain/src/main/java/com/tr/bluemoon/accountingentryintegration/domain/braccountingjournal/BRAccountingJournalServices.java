package com.tr.bluemoon.accountingentryintegration.domain.braccountingjournal;

import com.tr.bluemoon.bracct.commons.UserInfo;
import com.tr.bluemoon.brtap.commons.util.UuidHelper;
import com.tr.bluemoon.commons.token.authentication.BluemoonCompanyServicesTokenV2;
import com.tr.bluemoon.domainawareness.environment.Environments;
import java.net.URI;
import java.util.UUID;
import javax.inject.Inject;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

public class BRAccountingJournalServices {

  private static final Logger LOGGER = LoggerFactory.getLogger(BRAccountingJournalServices.class);

  private final Client client;
  private final UserInfo userInfo;

  @Inject
  public BRAccountingJournalServices(UserInfo userInfo) {
    this.client = ClientBuilder.newClient();
    this.userInfo = userInfo;
  }

  public JournalResponseDto getThirdPartyIntegration(UUID protocolId) {
    final var uri = UriBuilder.fromPath(getBRAccountingJournalServicesServicesRoute())
        .path("{version}")
        .path("third-party-integration")
        .path("{protocolId}")
        .build("v1", protocolId);

    final Response journalResponse = getJournalResponse(uri);

    JournalResponseDto journalResponseDto;
    if (HttpStatus.OK.value() == journalResponse.getStatus()) {
      try {
        journalResponseDto = journalResponse.readEntity(JournalResponseDto.class);
      } catch (Exception exc) {
        journalResponseDto = new JournalResponseDto();
        LOGGER.error("Error reading journal response", exc);
      }
    } else {
      journalResponseDto = new JournalResponseDto();
    }
    return journalResponseDto;
  }

  protected Response getJournalResponse(URI uri) {
    final String authHeader = BluemoonCompanyServicesTokenV2
        .createForCompany(UuidHelper.fromUUID(userInfo.getCompanyId()))
        .generateAuthHeader();

    return client.target(uri)
        .request(MediaType.APPLICATION_JSON)
        .header(HttpHeaders.AUTHORIZATION, authHeader)
        .get();
  }

  protected String getBRAccountingJournalServicesServicesRoute() {
    return Environments.get().getRoutingTable().get("BRAccountingJournalServices");
  }

}
