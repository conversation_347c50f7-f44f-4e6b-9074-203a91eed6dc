package com.tr.bluemoon.accountingentryintegration.domain.cache;

import com.fasterxml.jackson.core.type.TypeReference;
import com.tr.bluemoon.bracct.commons.jpa.BaseDao;
import com.tr.bluemoon.bracct.commons.jpa.PersistentEntity;
import java.io.Serializable;
import java.util.List;
import javax.persistence.EntityManager;

public abstract class CacheBaseDao<E extends PersistentEntity<T>, T extends Serializable>
    extends BaseDao<E, T> {

  protected final CacheManager cacheManager;

  public CacheBaseDao(EntityManager entityManager) {
    this(entityManager, new CacheManager());
  }

  public CacheBaseDao(EntityManager entityManager, long cacheExpireSeconds) {
    this(entityManager, new CacheManager(cacheExpireSeconds));
  }

  public CacheBaseDao(EntityManager entityManager, CacheManager cacheManager) {
    super(entityManager);
    this.cacheManager = cacheManager;
  }

  /**
   * Save or update an entity.
   *
   * @param entity entity to persist
   * @return persisted entity
   */
  @Override
  public E save(E entity) {
    if (!entity.isNew()) {
      clearCache(entity.getId());
    }
    return super.save(entity);
  }

  /**
   * Delete an entity.
   *
   * @param entity entity to be deleted.
   */
  @Override
  public void delete(E entity) {
    clearCache(entity.getId());
    super.delete(entity);
  }

  public abstract String getCacheKey(T id);

  public void clearCache(T id) {
    cacheManager.evict(getCacheKey(id));
  }

  public E get(T id, TypeReference<E> typed, CacheLoader<E> loader) {
    return get(getCacheKey(id), typed, loader);
  }

  public E get(String key, TypeReference<E> typed, CacheLoader<E> loader) {
    return cacheManager.get(key, typed, loader);
  }

  public List<E> getList(T id, TypeReference<List<E>> typed, CacheLoader<List<E>> loader) {
    return getList(getCacheKey(id), typed, loader);
  }

  public List<E> getList(String key, TypeReference<List<E>> typed, CacheLoader<List<E>> loader) {
    return cacheManager.get(key, typed, loader);
  }

  public CacheManager getCacheManager() {
    return cacheManager;
  }
}
