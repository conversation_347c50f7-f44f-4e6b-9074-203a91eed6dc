package com.tr.bluemoon.accountingentryintegration.domain.cache;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.util.StdDateFormat;
import io.lettuce.core.SetArgs;
import io.lettuce.core.api.sync.RedisCommands;
import java.io.IOException;
import java.util.List;
import javax.json.JsonException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Holds the cache control for some entity.
 *
 * <AUTHOR>
 */
public class CacheManager {

  private static final Logger LOGGER = LoggerFactory.getLogger(CacheManager.class);
  private static final long CACHE_EXPIRE_SECONDS_DEFAULT = 86400; // 24h

  private long cacheExpireSeconds;

  public CacheManager() {
    this(CACHE_EXPIRE_SECONDS_DEFAULT);
  }

  public CacheManager(long cacheExpireSeconds) {
    this.cacheExpireSeconds = cacheExpireSeconds;
  }

  public void setCacheExpireSeconds(long cacheExpireSeconds) {
    this.cacheExpireSeconds = cacheExpireSeconds;
  }

  public <T> T get(String key, TypeReference<T> type) {
    final RedisCommands<String, String> command = sync();
    String cached = command.get(key);

    return cached == null ? null : readValue(cached, type);
  }

  public <T> T get(String key, TypeReference<T> type, CacheLoader<T> loader) {
    return get(key, type, loader, true);
  }

  public <T> T get(String key, TypeReference<T> type, CacheLoader<T> loader, boolean setOnLoad) {
    T data = null;

    try {
      data = get(key, type);
      if (data == null) {
        data = loader.load();
        if (setOnLoad) {
          set(key, data);
        }
      }
    } catch (JsonException e) {
      LOGGER.error("Error while trying to get object from RedisCache: " + e.getMessage(), e);
      data = loader.load();
      if (setOnLoad) {
        set(key, data);
      }
    }
    return data;
  }

  protected void set(String key, Object data) {
    final RedisCommands<String, String> command = sync();
    String cached = writeValueAsString(data);
    command.set(key, cached, SetArgs.Builder.ex(cacheExpireSeconds));
  }

  /**
   * Delete one or more keys and its value from cache.
   *
   * @param keys keys to be deleted
   * @return Long integer-reply The number of keys that were removed.
   */
  public Long evict(String... keys) {
    final RedisCommands<String, String> command = sync();
    return command.del(keys);
  }

  /**
   * Delete one or more keys and its value from cache.
   *
   * @param pattern of keys to be deleted
   * @return Long integer-reply The number of keys that were removed.
   */
  public Long evictAll(String pattern) {
    List<String> keys = sync().keys(pattern);
    if (!keys.isEmpty()) {
      String[] values = keys.toArray(new String[keys.size()]);
      return evict(values);
    }
    return 0L;
  }

  protected RedisCommands<String, String> sync() {
    return OnvioRedisConnection.getConnection().sync();
  }

  protected <T> T readValue(String value, TypeReference<T> valueTypeRef) {
    try {
      return getObjectMapper().readValue(value, valueTypeRef);
    } catch (Exception e) {
      throw new JsonException("Cannot deserialize string to object", e);
    }
  }

  protected String writeValueAsString(Object object) {
    try {
      return getObjectMapper().writeValueAsString(object);
    } catch (IOException e) {
      throw new JsonException("Cannot serialize java object to string", e);
    }
  }

  protected ObjectMapper getObjectMapper() {
    ObjectMapper mapper = new ObjectMapper();
    mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    mapper.registerModule(new com.fasterxml.jackson.datatype.jsr310.JavaTimeModule());
    mapper.setDateFormat(new StdDateFormat().withColonInTimeZone(true));
    return mapper;
  }
}
