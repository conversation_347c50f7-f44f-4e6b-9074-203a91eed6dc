package com.tr.bluemoon.accountingentryintegration.domain.cache;

import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import io.lettuce.core.api.StatefulRedisConnection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Onvio Shared Redis connection.
 *
 * <AUTHOR>
 */
public class OnvioRedisConnection {

  private static final Logger LOGGER = LoggerFactory.getLogger(OnvioRedisConnection.class);

  private static final String REDIS_HOST_KEY = System.getProperty("RedisHost");
  private static final String REDIS_PORT_KEY = System.getProperty("RedisPort");

  private static RedisClient client;
  private static StatefulRedisConnection<String, String> connection;

  private OnvioRedisConnection() {
    // Instances must be created using getConnection()
  }

  public static StatefulRedisConnection<String, String> getConnection() {
    if (connection == null || !connection.isOpen()) {
      LOGGER.info("Attempting to reestablish connection to {}", REDIS_HOST_KEY);

      start();
    }
    return connection;
  }

  public static void start() {
    LOGGER.info(
        "Starting Shared Redis connection to {} on port {}", REDIS_HOST_KEY, REDIS_PORT_KEY);

    RedisURI uri = RedisURI.create(REDIS_HOST_KEY, Integer.parseInt(REDIS_PORT_KEY));
    client = RedisClient.create(uri);
    connection = client.connect();
  }

  public static void stop() {
    LOGGER.info("Closing Shared Redis connection to {} on port {}", REDIS_HOST_KEY, REDIS_PORT_KEY);

    if (connection != null && connection.isOpen()) {
      connection.close();
    }

    if (client != null) {
      client.shutdown();
    }
  }
}
