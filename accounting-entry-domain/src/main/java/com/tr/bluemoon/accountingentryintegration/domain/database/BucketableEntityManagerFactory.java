package com.tr.bluemoon.accountingentryintegration.domain.database;

import com.tr.bluemoon.accountingentryintegration.database.BucketedPersistenceManager;
import com.tr.bluemoon.accountingentryintegration.database.PersistenceManager;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitPostProcessor;
import com.tr.bluemoon.bracct.commons.jpa.model.CompanyOwnershipEntity;
import com.tr.bluemoon.brtap.commons.security.ContextHolder;
import com.tr.bluemoon.brtap.commons.util.UuidHelper;
import com.tr.bluemoon.commons.configuration.BlueMoonDomain;
import java.util.Map;
import java.util.UUID;
import javax.persistence.Cache;
import javax.persistence.EntityGraph;
import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.PersistenceException;
import javax.persistence.PersistenceUnitUtil;
import javax.persistence.Query;
import javax.persistence.SynchronizationType;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.metamodel.Metamodel;

public class BucketableEntityManagerFactory implements EntityManagerFactory {

  private static final String PERSISTENCE_UNIT = "BR_ACCOUNTING_ENTRY_INTEGRATION_COMPANY_PU";
  private static final String BUCKET_TYPE = "bracctaccountingentryintegrationservices";
  private static final String DATASOURCE_KEY = "bucket_datasource";

  @Override
  public EntityManager createEntityManager() {
    final var em = getEntityManagerFactory().createEntityManager();
    prepareEntityManager(em);
    return em;
  }

  @Override
  public EntityManager createEntityManager(Map map) {
    final var em = getEntityManagerFactory().createEntityManager(map);
    prepareEntityManager(em);
    return em;
  }

  @Override
  public EntityManager createEntityManager(SynchronizationType synchronizationType) {
    final var em = getEntityManagerFactory().createEntityManager(synchronizationType);
    prepareEntityManager(em);
    return em;
  }

  @Override
  public EntityManager createEntityManager(SynchronizationType synchronizationType, Map map) {
    final var em = getEntityManagerFactory().createEntityManager(synchronizationType, map);
    prepareEntityManager(em);
    return em;
  }

  private void prepareEntityManager(EntityManager entityManager) {
    final ContextHolder contextHolder = RabbitPostProcessor.getContext();
    final UUID storageId = contextHolder.getCompanyId();

    entityManager.setProperty("domain", BlueMoonDomain.getExternal());
    entityManager.setProperty(CompanyOwnershipEntity.TENANT_CONTEXT_PROPERTY, storageId);
  }

  @Override
  public CriteriaBuilder getCriteriaBuilder() {
    return getEntityManagerFactory().getCriteriaBuilder();
  }

  @Override
  public Metamodel getMetamodel() {
    return getEntityManagerFactory().getMetamodel();
  }

  @Override
  public boolean isOpen() {
    return getEntityManagerFactory().isOpen();
  }

  @Override
  public void close() {
    getEntityManagerFactory().close();
  }

  @Override
  public Map<String, Object> getProperties() {
    return getEntityManagerFactory().getProperties();
  }

  @Override
  public Cache getCache() {
    return getEntityManagerFactory().getCache();
  }

  @Override
  public PersistenceUnitUtil getPersistenceUnitUtil() {
    return getEntityManagerFactory().getPersistenceUnitUtil();
  }

  @Override
  public void addNamedQuery(String name, Query query) {
    getEntityManagerFactory().addNamedQuery(name, query);
  }

  @Override
  public <T> T unwrap(Class<T> cls) {
    return getEntityManagerFactory().unwrap(cls);
  }

  @Override
  public <T> void addNamedEntityGraph(String graphName, EntityGraph<T> entityGraph) {
    getEntityManagerFactory().addNamedEntityGraph(graphName, entityGraph);
  }

  private EntityManagerFactory getEntityManagerFactory() throws PersistenceException {
    final ContextHolder contextHolder = RabbitPostProcessor.getContext();
    final String storageId = UuidHelper.fromUUID(contextHolder.getCompanyId());

    PersistenceManager persistenceManager =
        getPersistenceManager().getBucket(storageId, DATASOURCE_KEY);
    return persistenceManager.getFactory();
  }

  /**
   * Gets a persistence manager.
   *
   * @return an instance of {@link BucketedPersistenceManager} for a given type of bucket
   */
  private BucketedPersistenceManager getPersistenceManager() {
    return BucketedPersistenceManager.getInstance(BUCKET_TYPE, PERSISTENCE_UNIT);
  }
}
