package com.tr.bluemoon.accountingentryintegration.domain.database;

import com.tr.bluemoon.accountingentryintegration.database.BluemoonBucketPersistenceManager;
import com.tr.bluemoon.accountingentryintegration.database.BucketedPersistenceManager;
import com.tr.bluemoon.bracct.commons.UserInfo;
import com.tr.bluemoon.bracct.commons.jpa.model.CompanyOwnershipEntity;
import com.tr.bluemoon.bracct.commons.util.UuidUtils;
import com.tr.bluemoon.commons.configuration.BlueMoonDomain;
import java.io.Serializable;
import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.inject.Any;
import javax.enterprise.inject.Disposes;
import javax.enterprise.inject.Produces;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import org.apache.deltaspike.jpa.api.transaction.TransactionScoped;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Handles the entire life cycle of an entity manager.
 *
 * <AUTHOR>
 */
@ApplicationScoped
@Configuration
@EnableTransactionManagement
public class BucketableEntityManagerProducer implements Serializable {

  private static final long serialVersionUID = 1L;

  private static final Logger LOGGER =
      LoggerFactory.getLogger(BucketableEntityManagerProducer.class);

  private static final String PERSISTENCE_UNIT = "BR_ACCOUNTING_ENTRY_INTEGRATION_COMPANY_PU";
  private static final String BUCKET_TYPE = "bracctaccountingentryintegrationservices";
  private static final String DATASOURCE_KEY = "bucket_datasource";

  /**
   * Gets a persistence manager.
   *
   * @return an instance of {@link BucketedPersistenceManager} for a given type of bucket
   */
  public static BucketedPersistenceManager getPersistenceManager() {
    return BucketedPersistenceManager.getInstance(BUCKET_TYPE, PERSISTENCE_UNIT);
  }

  /**
   * Executes a simple test query to ensure the database is running up.
   *
   * @return true if database is up, otherwise false
   */
  public static boolean isDatabasesUp() {
    try {
      final BucketedPersistenceManager bpm = getPersistenceManager();
      bpm.runOnAllBuckets(
          em -> {
            String testQuery =
                new BluemoonBucketPersistenceManager().getConnectionTestQuery(DATASOURCE_KEY);
            final Query query = em.createNativeQuery(testQuery);
            query.getSingleResult();
          });

      return true;
    } catch (Exception ex) {
      LOGGER.error(ex.getMessage(), ex);
      return false;
    }
  }

  @Produces
  @TransactionScoped
  public EntityManager createEntityManager(final @Any UserInfo userInfo) {
    String storageId = UuidUtils.fromUUID(userInfo.getCompanyId());
    EntityManager em =
        getPersistenceManager().getBucket(storageId, DATASOURCE_KEY).createEntityManager();
    em.setProperty("domain", BlueMoonDomain.getExternal());
    em.setProperty(CompanyOwnershipEntity.TENANT_CONTEXT_PROPERTY, userInfo.getCompanyId());

    return em;
  }

  @Bean
  public BucketableEntityManagerFactory entityManagerFactory() {
    return new BucketableEntityManagerFactory();
  }

  @Bean
  public PlatformTransactionManager transactionManager(
      BucketableEntityManagerFactory entityManagerFactory) {
    final JpaTransactionManager transactionManager = new JpaTransactionManager();
    transactionManager.setEntityManagerFactory(entityManagerFactory);
    return transactionManager;
  }

  /**
   * Close the entity manager used in the current transaction.
   *
   * @param manager manager
   */
  public void closeEntityManager(@Disposes EntityManager manager) {
    if (manager.isOpen()) {
      manager.close();
    }
  }
}
