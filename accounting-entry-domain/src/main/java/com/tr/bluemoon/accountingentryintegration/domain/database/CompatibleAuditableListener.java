package com.tr.bluemoon.accountingentryintegration.domain.database;

import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitPostProcessor;
import com.tr.bluemoon.bracct.commons.jpa.AuditableEntity;
import com.tr.bluemoon.bracct.commons.jpa.AuditableListener;
import java.time.Clock;
import java.time.LocalDateTime;
import java.util.UUID;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;

public class CompatibleAuditableListener extends AuditableListener {

  /** Useful attribute used in unit tests to mock fixed clocks. */
  // Sonar does not like very much that we name as clock because it shadows clock from super class.
  Clock timer;

  public CompatibleAuditableListener() {
    this.timer = Clock.systemDefaultZone();
  }

  @PrePersist
  @Override
  public void onPrePersist(final AuditableEntity entity) {
    // If null, we are running on CDI
    if (RabbitPostProcessor.getContextOrDefault(null) == null) {
      invokeParentPrePersist(entity);
    } else {
      if (entity.getCreated() == null) {
        entity.setCreated(LocalDateTime.now(timer));
      }
      if (entity.getCreatedBy() == null) {
        entity.setCreatedBy(getOwner());
      }
    }
  }

  // To allow to be mocked on tests
  protected void invokeParentPrePersist(AuditableEntity entity) {
    super.onPrePersist(entity);
  }

  /**
   * Set values for changed and changedBy fields.
   *
   * @param entity updated entity
   */
  @PreUpdate
  @Override
  public void onPreUpdate(final AuditableEntity entity) {
    // If null, we are running on CDI
    if (RabbitPostProcessor.getContextOrDefault(null) == null) {
      invokeParentPreUpdate(entity);
    } else {
      entity.setChanged(LocalDateTime.now(timer));
      entity.setChangedBy(getOwner());
    }
  }

  // To allow to be mocked on tests
  protected void invokeParentPreUpdate(AuditableEntity entity) {
    super.onPreUpdate(entity);
  }

  @Override
  protected UUID getOwner() {
    // If null, we are running on CDI
    if (RabbitPostProcessor.getContextOrDefault(null) == null) {
      return super.getOwner();
    }
    return RabbitPostProcessor.getContext().getContactId();
  }
}
