package com.tr.bluemoon.accountingentryintegration.domain.database;

import com.tr.bluemoon.accountingentryintegration.database.BluemoonBucketPersistenceManager;
import com.tr.bluemoon.accountingentryintegration.database.BucketedPersistenceManager;
import com.tr.bluemoon.bracct.commons.UserInfo;
import com.tr.bluemoon.bracct.commons.jpa.model.CompanyOwnershipEntity;
import com.tr.bluemoon.bracct.commons.util.UuidUtils;
import com.tr.bluemoon.commons.configuration.BlueMoonDomain;
import java.io.Serializable;
import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.inject.Any;
import javax.enterprise.inject.Disposes;
import javax.enterprise.inject.Produces;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import org.apache.deltaspike.jpa.api.transaction.TransactionScoped;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Handles the entire life cycle of an entity manager for invoicing database.
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class InvoicingBucketableEntityManagerProducer implements Serializable {

  private static final long serialVersionUID = 1L;

  private static final Logger LOGGER =
      LoggerFactory.getLogger(InvoicingBucketableEntityManagerProducer.class);

  private static final String PERSISTENCE_UNIT = "BR_ACCT_INVOICE_INTEGRATION_COMPANY_PU";
  private static final String BUCKET_TYPE = "bracctinvoiceintegrationservices";
  private static final String DATASOURCE_KEY = "bucket_datasource_invoicing";

  /**
   * Gets a persistence manager.
   *
   * @return an instance of {@link BucketedPersistenceManager} for a given type of bucket
   */
  public static BucketedPersistenceManager getPersistenceManager() {
    return BucketedPersistenceManager.getInstance(BUCKET_TYPE, PERSISTENCE_UNIT);
  }

  /**
   * Executes a simple test query to ensure the database is running up.
   *
   * @return true if database is up, otherwise false
   */
  public static boolean isDatabasesUp() {
    try {
      final BucketedPersistenceManager bpm = getPersistenceManager();
      bpm.runOnAllBuckets(
          em -> {
            String testQuery =
                new BluemoonBucketPersistenceManager().getConnectionTestQuery(DATASOURCE_KEY);
            final Query query = em.createNativeQuery(testQuery);
            query.getSingleResult();
          });

      return true;
    } catch (Exception ex) {
      LOGGER.error(ex.getMessage(), ex);
      return false;
    }
  }

  @Produces
  @InvoicingBucketableDatabase
  @TransactionScoped
  public EntityManager createEntityManager(final @Any UserInfo userInfo) {
    String storageId = UuidUtils.fromUUID(userInfo.getCompanyId());
    EntityManager em =
        getPersistenceManager().getBucket(storageId, DATASOURCE_KEY).createEntityManager();
    em.setProperty("domain", BlueMoonDomain.getExternal());
    em.setProperty(CompanyOwnershipEntity.TENANT_CONTEXT_PROPERTY, userInfo.getCompanyId());

    return em;
  }

  /**
   * Close the entity manager used in the current transaction.
   *
   * @param manager manager
   */
  public void closeEntityManager(@Disposes @InvoicingBucketableDatabase EntityManager manager) {
    if (manager.isOpen()) {
      manager.close();
    }
  }
}
