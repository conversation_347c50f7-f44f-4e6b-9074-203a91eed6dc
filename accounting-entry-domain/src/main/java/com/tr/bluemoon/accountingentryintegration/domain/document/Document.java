package com.tr.bluemoon.accountingentryintegration.domain.document;

import com.tr.bluemoon.accountingentryintegration.domain.database.CompatibleAuditableListener;
import com.tr.bluemoon.bracct.commons.jpa.UUIDGenerator;
import com.tr.bluemoon.bracct.commons.jpa.converter.LocalDateTimeToDateConverter;
import com.tr.bluemoon.bracct.commons.jpa.converter.UuidToUuidConverter;
import com.tr.bluemoon.bracct.commons.jpa.model.CompanyOwnershipEntity;
import java.time.Clock;
import java.time.LocalDateTime;
import java.util.UUID;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.ExcludeSuperclassListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * Entity to handle document data.
 *
 * <AUTHOR> Lanzendorf
 */
@Getter
@Setter
@Entity
@Table(name = "document")
@ExcludeSuperclassListeners // Auditable from the parent is very coupled with UserInfo and CDI
@EntityListeners(
    value = {
      CompatibleAuditableListener.class
    }) // Makes auditable compatible with both CDI and RabbitPostProcessor
public class Document extends CompanyOwnershipEntity<UUID> {

  private static final long serialVersionUID = 1L;

  @Id
  @GeneratedValue(generator = UUIDGenerator.UUID_GENERATOR)
  @Convert(converter = UuidToUuidConverter.class)
  @Column(name = "document_id", nullable = false, insertable = true, updatable = false)
  private UUID id;

  @NotNull
  @Column(name = "file_id", insertable = true, updatable = true)
  @Convert(converter = UuidToUuidConverter.class)
  private UUID fileId;

  @NotNull
  @Column(name = "client_id", insertable = true, updatable = true)
  @Convert(converter = UuidToUuidConverter.class)
  private UUID clientId;

  @Column(name = "file_path")
  private String filePath;

  @NotNull
  @Column(name = "status")
  @Enumerated(EnumType.STRING)
  private DocumentStatus status;

  @Column(name = "last_status_on")
  @Convert(converter = LocalDateTimeToDateConverter.class)
  private LocalDateTime lastStatusOn;

  @Column(name = "sent_date")
  @Convert(converter = LocalDateTimeToDateConverter.class)
  private LocalDateTime sentDate;

  @Column(name = "download_attempts")
  private int downloadAttempts = 0;

  @Column(name = "is_downloaded")
  private boolean isDownloaded = false;

  @Column(name = "last_download_date")
  @Convert(converter = LocalDateTimeToDateConverter.class)
  private LocalDateTime lastDownloadDate;

  public Document() {
    this.status = DocumentStatus.AWAITING_TO_SEND;
    this.lastStatusOn = LocalDateTime.now(Clock.systemUTC());
  }

  public void setStatus(DocumentStatus status) {
    if (!this.status.equals(status)) {
      this.lastStatusOn = LocalDateTime.now(Clock.systemUTC());
    }
    this.status = status;
  }
}
