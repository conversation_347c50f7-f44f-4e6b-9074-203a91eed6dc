package com.tr.bluemoon.accountingentryintegration.domain.document;

import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitMQEvent;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DocumentAvailableEvent implements RabbitMQEvent {

  private UUID id;
  private LocalDateTime created;
  // This is an 1 hour pre-signed URL that is generated at the moment message is sent to the socket
  private String shortUrl;
  private OffsetDateTime shortUrlCreatedAt;
  // This is an endpoint that generates 5 minutes pre-signed url on demand
  private String location;
  private UUID clientId;

  public DocumentAvailableEvent(UUID id) {
    this.id = id;
  }
}
