package com.tr.bluemoon.accountingentryintegration.domain.document;

import static com.tr.bluemoon.accountingentryintegration.domain.document.util.DocumentUrlUtil.downloadUrl;

import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitConfig;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitMQEnqueuer;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitMQListEvent;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitPostProcessor;
import com.tr.bluemoon.accountingentryintegration.domain.aws.S3Service;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Sends a message to the Socket Services regarding new valid XML file available for downloading.
 *
 * <AUTHOR>
 */
@Component
public class DocumentDeliveryProducer {

  private static final Duration PRE_SIGNED_SHORT_DURATION = Duration.ofHours(1);
  private static final Integer MAX_PAGE_SIZE = 1000;
  private final RabbitMQEnqueuer enqueuer;
  private final S3Service s3Service;

  @Autowired
  public DocumentDeliveryProducer(RabbitMQEnqueuer enqueuer, S3Service s3Service) {
    this.enqueuer = enqueuer;
    this.s3Service = s3Service;
  }

  public void sendToSocket(Document document) {
    sendToSocket(List.of(document), false);
  }

  public void sendToSocket(List<Document> documents, boolean isPackage) {
    final boolean hasMore = documents.size() > MAX_PAGE_SIZE;
    if (hasMore) {
      // Remove last to return only MAX_PAGE_SIZE. Last was used to determine if it had more.
      documents.remove(documents.size() - 1);
    }

    final List<DocumentAvailableEvent> events = new ArrayList<>();

    for (Document document : documents) {
      // Set file id because it is the protocol
      final var event = new DocumentAvailableEvent(document.getFileId());
      event.setCreated(document.getCreated());
      event.setShortUrlCreatedAt(OffsetDateTime.now());
      event.setShortUrl(
          s3Service.getPreSignedDownloadUrl(document.getFilePath(), PRE_SIGNED_SHORT_DURATION));
      event.setLocation(downloadUrl(document.getId()));
      event.setClientId(document.getClientId());

      events.add(event);
    }

    final RabbitMQListEvent<DocumentAvailableEvent> pack =
        new RabbitMQListEvent<>(events, isPackage, hasMore);
    enqueuer.publishMessage(
        pack,
        RabbitConfig.DOCUMENT_AVAILABLE_EVENT,
        RabbitConfig.ACCOUNTING_ENTRY_INTEGRATION_EXCHANGE,
        RabbitPostProcessor.getContext());
  }




}
