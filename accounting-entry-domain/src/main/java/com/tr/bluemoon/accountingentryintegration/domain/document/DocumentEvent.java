package com.tr.bluemoon.accountingentryintegration.domain.document;

import com.tr.bluemoon.accountingentryintegration.domain.EntityEvent;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class DocumentEvent extends EntityEvent {

  private UUID id;
  private UUID fileId;
  private UUID clientId;
  private String path;
  private LocalDateTime created;

}
