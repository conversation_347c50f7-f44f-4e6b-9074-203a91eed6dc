package com.tr.bluemoon.accountingentryintegration.domain.document;

import static com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitConfig.ACCOUNTING_ENTRY_INTEGRATION_EXCHANGE;
import static com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitConfig.ACCOUNTING_ENTRY_INTEGRATION_RETRY_EXCHANGE;

import com.tr.bluemoon.accountingentryintegration.domain.amqp.BindingDefinition;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.CloudEventMessageConverter;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.ExchangeDefinition;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.QueueDefinition;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitPostProcessor;
import com.tr.bluemoon.brtap.commons.domainawareness.RoutingPaths;
import java.util.concurrent.TimeUnit;
import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.inject.Produces;
import javax.inject.Named;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.RabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Setup the queues to received orders for getting the missed documents in the meantime agent was
 * disconnected from the socket.
 *
 * <AUTHOR>
 */
@Configuration
public class DocumentMissedConfig {

  // Queues
  public static final String DOCUMENT_MISSED_QUEUE =
      "com.tr.bluemoon.subscriber.brtap.accountingentryintegration.document-missed";
  public static final String DOCUMENT_MISSED_RETRY_QUEUE =
      "com.tr.bluemoon.subscriber.brtap.accountingentryintegration.document-missed-retry";
  public static final String DOCUMENT_MISSED_DEAD_LETTER_QUEUE =
      "com.tr.bluemoon.subscriber.brtap.accountingentryintegration.document-missed-dlq";

  // Events
  public static final String DOCUMENT_MISSED_EVENT =
      "com.tr.bluemoon.event.brtap.accountingentryintegration.document.v1.missed";
  // Configs
  public static final long RETRY_MESSAGE_TTL = TimeUnit.MINUTES.toMillis(10);
  public static final long DEAD_LETTER_MESSAGE_TTL = TimeUnit.DAYS.toMillis(60);
  @Value("${brtap.accountingentryintegration.document-missed.prefetch:1}")
  private Integer documentMissedQueuePrefetch;

  // Declare Queues --------------------------------------------------------------------------------
  @ApplicationScoped
  @Produces
  @Bean
  public QueueDefinition documentMissedQueue() {
    final var queue = new QueueDefinition(DOCUMENT_MISSED_QUEUE, true);
    queue.addArgument("x-dead-letter-exchange", ACCOUNTING_ENTRY_INTEGRATION_RETRY_EXCHANGE);
    return queue;
  }

  @ApplicationScoped
  @Produces
  @Bean
  public QueueDefinition documentMissedRetryQueue() {
    final var queue = new QueueDefinition(DOCUMENT_MISSED_RETRY_QUEUE, true);
    queue.addArgument("x-message-ttl", RETRY_MESSAGE_TTL);
    queue.addArgument("x-dead-letter-exchange", ACCOUNTING_ENTRY_INTEGRATION_EXCHANGE);
    return queue;
  }

  @ApplicationScoped
  @Produces
  @Bean
  public QueueDefinition documentMissedDeadLetterQueue() {
    final var queue = new QueueDefinition(DOCUMENT_MISSED_DEAD_LETTER_QUEUE, true);
    queue.addArgument("x-message-ttl", DEAD_LETTER_MESSAGE_TTL);
    return queue;
  }

  // Declare Bindings ------------------------------------------------------------------------------
  @ApplicationScoped
  @Produces
  @Bean
  public BindingDefinition documentMissedAccountingEntryIntegrationCreateBindingPubSub(
      @Named("accountingEntryIntegrationExchange") @Qualifier("accountingEntryIntegrationExchange")
          ExchangeDefinition accountingEntryIntegrationExchange) {
    final var queue = documentMissedQueue();
    return new BindingDefinition(accountingEntryIntegrationExchange, queue, DOCUMENT_MISSED_EVENT);
  }

  @ApplicationScoped
  @Produces
  @Bean
  public BindingDefinition documentMissedAccountingEntryIntegrationCreateBindingRetry(
      @Named("accountingEntryIntegrationRetryExchange")
          @Qualifier("accountingEntryIntegrationRetryExchange")
          ExchangeDefinition accountingEntryIntegrationRetryExchange) {
    final var queue = documentMissedRetryQueue();
    return new BindingDefinition(
        accountingEntryIntegrationRetryExchange, queue, DOCUMENT_MISSED_EVENT);
  }

  @ApplicationScoped
  @Produces
  @Bean
  public BindingDefinition documentMissedAccountingEntryIntegrationCreateBindingDeadLetter(
      @Named("accountingEntryIntegrationDeadLetterExchange")
          @Qualifier("accountingEntryIntegrationDeadLetterExchange")
          ExchangeDefinition accountingEntryIntegrationDeadLetterExchange) {
    final var queue = documentMissedDeadLetterQueue();
    return new BindingDefinition(
        accountingEntryIntegrationDeadLetterExchange, queue, DOCUMENT_MISSED_EVENT);
  }

  // Config for Spring Rabbit Listener -------------------------------------------------------------
  @Bean
  public RabbitListenerContainerFactory<SimpleMessageListenerContainer>
      documentMissedQueueContainerFactory(
          ConnectionFactory rabbitConnectionFactory, RoutingPaths routingPaths) {
    final CloudEventMessageConverter converter = new CloudEventMessageConverter();

    SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
    factory.setConnectionFactory(rabbitConnectionFactory);
    factory.setPrefetchCount(documentMissedQueuePrefetch);
    factory.setMessageConverter(converter);
    factory.setAfterReceivePostProcessors(new RabbitPostProcessor(converter, routingPaths));

    return factory;
  }
}
