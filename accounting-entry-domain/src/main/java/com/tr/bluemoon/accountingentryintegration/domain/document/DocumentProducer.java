package com.tr.bluemoon.accountingentryintegration.domain.document;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tr.bluemoon.accountingentryintegration.domain.KafkaProducerConfig;
import com.tr.bluemoon.accountingentryintegration.domain.TopicProducer;
import com.tr.bluemoon.accountingentryintegration.domain.outboxevent.OutboxEventService;
import io.cloudevents.CloudEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Component
public class DocumentProducer extends TopicProducer {

  private static final String PUBLISH_EVENT = "com.tr.bluemoon.event.brtap.accountingentryintegration.document.v1.publish";

  @Autowired
  public DocumentProducer(ObjectMapper objectMapper,
      OutboxEventService outboxEventService, KafkaTemplate<String, CloudEvent> kafkaTemplate) {
    super(objectMapper, outboxEventService, kafkaTemplate);
  }

  public void publishDocument(Document document) {
    final DocumentEvent documentEvent = DocumentEventMapper.toEvent(document);

    //  The functionality of saving in outbox is going to be
    //  disabled because it will be configured in a future moment
    //  publishEntityToOutbox(documentEvent, PUBLISH_EVENT, KafkaProducerConfig.DOCUMENT_TOPIC);
    publishEntityToKafka(documentEvent, PUBLISH_EVENT, KafkaProducerConfig.DOCUMENT_TOPIC);
  }
}
