package com.tr.bluemoon.accountingentryintegration.domain.document;

import static com.tr.bluemoon.bracct.commons.jpa.query.QueryUtils.attribute;

import com.tr.bluemoon.bracct.commons.jpa.query.FromQueryBuilder;
import com.tr.bluemoon.bracct.commons.jpa.query.QueryBuilder;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import org.eclipse.persistence.jpa.JpaQuery;
import org.springframework.stereotype.Repository;

/**
 * Repository to handle persistence methods for {@link Document}. entity
 *
 * <AUTHOR>
 */
@Repository
public class DocumentRepository {

  private static final Integer MAX_PAGE_SIZE = 1000;

  @Inject @PersistenceContext private EntityManager entityManager;

  public Document save(Document entity) {
    if (entity.isNew()) {
      entityManager.persist(entity);

    } else {
      entity = entityManager.merge(entity);
    }

    return entity;
  }

  public Document findById(UUID id) {
    return entityManager.find(Document.class, id);
  }

  public List<Document> findAllByStartDate(LocalDateTime startDate) {
    FromQueryBuilder<Document> fromQueryBuilder =
        QueryBuilder.Factory.createQueryBuilder(entityManager, Document.class).from(Document.class);

    fromQueryBuilder.orderBy("created asc");

    if (startDate != null) {
      fromQueryBuilder.where(attribute("created").greaterThan(startDate));
    }

    TypedQuery<Document> query = fromQueryBuilder.getQuery(entityManager);
    query.setMaxResults(MAX_PAGE_SIZE + 1); // Get one more to determine if hasMore

    ((JpaQuery<Document>) query).getDatabaseQuery().dontMaintainCache();

    return query.getResultList();
  }
}
