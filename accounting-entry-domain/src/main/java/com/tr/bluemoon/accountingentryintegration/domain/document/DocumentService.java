package com.tr.bluemoon.accountingentryintegration.domain.document;

import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitPostProcessor;
import com.tr.bluemoon.accountingentryintegration.domain.aws.S3Service;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import javax.inject.Inject;
import org.apache.deltaspike.jpa.api.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Service class to handle {@link Document} business logic and coordinate persistence.
 *
 * <AUTHOR>
 */
@Service
public class DocumentService {

  private static final String DOCUMENT_FOLDER = "document";

  private final DocumentRepository repository;
  private final S3Service s3Service;

  @Inject
  @Autowired
  public DocumentService(DocumentRepository repository, S3Service s3Service) {
    this.repository = repository;
    this.s3Service = s3Service;
  }

  @Transactional
  @org.springframework.transaction.annotation.Transactional
  public Document save(Document entity) {
    Objects.requireNonNull(entity, "entity cannot be null");
    return repository.save(entity);
  }

  @Transactional(readOnly = true)
  @org.springframework.transaction.annotation.Transactional(readOnly = true)
  public Document findById(UUID id) {
    Objects.requireNonNull(id, "id cannot be null");
    return repository.findById(id);
  }

  // Take care, this is intended to be used only on AMQP process, because it is tied to
  // RabbitPostProcessor context.
  @Transactional
  @org.springframework.transaction.annotation.Transactional
  public Document createDocument(UUID fileId, UUID clientId, UUID companyId, String fileKey) {
    final var domain = RabbitPostProcessor.getContext().getBlueMoonDomain().getDomain();
    String filePath = s3Service.copyXml(fileKey, DOCUMENT_FOLDER, clientId, companyId, domain);

    Document document = new Document();
    document.setClientId(clientId);
    document.setFileId(fileId);
    document.setFilePath(filePath);

    return save(document);
  }

  @Transactional(readOnly = true)
  @org.springframework.transaction.annotation.Transactional(readOnly = true)
  public List<Document> findAllByStartDate(LocalDateTime startDate) {
    // If startDate exceeds 60 days then get only 60 days
    LocalDateTime limitDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIDNIGHT).minusDays(60);
    if (startDate == null || startDate.isBefore(limitDate)) {
      startDate = limitDate;
    }

    return repository.findAllByStartDate(startDate);
  }

  @Transactional
  @org.springframework.transaction.annotation.Transactional
  public void documentDownloaded(Document document) {
    document.setLastDownloadDate(LocalDateTime.now());
    document.setDownloadAttempts(document.getDownloadAttempts() + 1);
    document.setDownloaded(true);
    this.save(document);
  }
}
