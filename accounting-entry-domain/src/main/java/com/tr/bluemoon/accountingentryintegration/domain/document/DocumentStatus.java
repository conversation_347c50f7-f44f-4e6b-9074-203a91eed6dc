package com.tr.bluemoon.accountingentryintegration.domain.document;

import static com.tr.bluemoon.accountingentryintegration.domain.document.DocumentStatusMessage.E0;
import static com.tr.bluemoon.accountingentryintegration.domain.document.DocumentStatusMessage.E1;
import static com.tr.bluemoon.accountingentryintegration.domain.document.DocumentStatusMessage.S1;
import static com.tr.bluemoon.accountingentryintegration.domain.document.DocumentStatusMessage.S2;

import com.tr.bluemoon.accountingentryintegration.domain.file.IStatus;
import com.tr.bluemoon.accountingentryintegration.domain.file.IStatusContainer;
import lombok.Getter;

/**
 * {@link Document} status Enum.
 *
 * <AUTHOR>
 */
@Getter
public enum DocumentStatus implements IStatusContainer {
  AWAITING_TO_SEND(S1),
  DOCUMENT_NOT_FOUND(E1),
  GENERIC_ERROR(E0),
  SENT(S2);

  private final IStatus status;

  DocumentStatus(IStatus status) {
    this.status = status;
  }
}
