package com.tr.bluemoon.accountingentryintegration.domain.document;

import com.tr.bluemoon.accountingentryintegration.domain.file.IStatus;

/**
 * {@link Document} status messages Enum.
 *
 * <AUTHOR>
 */
public enum DocumentStatusMessage implements IStatus {
  S1("Aguardando envio. Por favor aguarde o arquivo ser processado."),
  S2("Enviado."),
  E0("Erro desconhecido."),
  E1("Documento não existe. Por favor consulte um documento existente."),
  E2("Arquivo não encontrado.");

  private final String message;

  DocumentStatusMessage(String message) {
    this.message = message;
  }

  public String getCode() {
    return this.name();
  }

  public String getMessage() {
    return this.message;
  }
}
