package com.tr.bluemoon.accountingentryintegration.domain.document.util;

import static com.tr.bluemoon.accountingentryintegration.domain.amqp.RoutingPathsConfig.BR_ACCOUNTING_ENTRY_INTEGRATION_SERVICES;

import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitPostProcessor;
import com.tr.bluemoon.brtap.commons.domainawareness.AbstractRoutes;
import java.util.Map;
import java.util.UUID;

public class DocumentUrlUtil {

  public static String downloadUrl(UUID documentId) {
    return Routes.DOWNLOAD_XML
        .on(BR_ACCOUNTING_ENTRY_INTEGRATION_SERVICES)
        .build(documentId)
        .toString();
  }

  private enum  Routes implements AbstractRoutes {
    DOWNLOAD_XML("/v1/documents/{documentId}/download");

    private final String path;

    Routes(String path) {
      this.path = path;
    }

    @Override
    public String getPath() {
      return path;
    }

    @Override
    public Map<String, String> getRoutingTable() {
      return RabbitPostProcessor.getContext().getRoutingTable();
    }
  }

}
