package com.tr.bluemoon.accountingentryintegration.domain.file;

import com.tr.bluemoon.accountingentryintegration.domain.database.CompatibleAuditableListener;
import com.tr.bluemoon.bracct.commons.jpa.UUIDGenerator;
import com.tr.bluemoon.bracct.commons.jpa.converter.LocalDateTimeToDateConverter;
import com.tr.bluemoon.bracct.commons.jpa.converter.UuidToUuidConverter;
import com.tr.bluemoon.bracct.commons.jpa.model.CompanyOwnershipEntity;
import java.time.Clock;
import java.time.LocalDateTime;
import java.util.UUID;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.ExcludeSuperclassListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * Entity to handle files data.
 *
 * <AUTHOR> Lanzendorf
 */
@Getter
@Setter
@Entity
@Table(name = "file")
@ExcludeSuperclassListeners // Auditable from the parent is very coupled with UserInfo and CDI
@EntityListeners(value = {CompatibleAuditableListener.class})
// Makes auditable compatible with both CDI and RabbitPostProcessor
public class File extends CompanyOwnershipEntity<UUID> {

  private static final long serialVersionUID = 1L;

  @Id
  @GeneratedValue(generator = UUIDGenerator.UUID_GENERATOR)
  @Convert(converter = UuidToUuidConverter.class)
  @Column(name = "file_id", nullable = false, updatable = false)
  private UUID id;

  @NotNull
  @Column(name = "client_id")
  @Convert(converter = UuidToUuidConverter.class)
  private UUID clientId;

  @NotNull
  @Column(name = "total_validation_attempts")
  private int totalValidationAttempts;

  @Column(name = "file_path")
  private String filePath;

  @NotNull
  @Column(name = "status")
  @Enumerated(EnumType.STRING)
  private FileStatus status;

  @Column(name = "last_status_on")
  @Convert(converter = LocalDateTimeToDateConverter.class)
  private LocalDateTime lastStatusOn;

  @Column(name = "is_onvio")
  private boolean isOnvio = false;

  public File() {
    this.status = FileStatus.AWAITING_VALIDATION;
    this.lastStatusOn = LocalDateTime.now(Clock.systemUTC());
  }

  @Override
  public UUID getId() {
    return id;
  }

  @Override
  public void setId(UUID id) {
    this.id = id;
  }

  public void setStatus(FileStatus status) {
    if (!this.status.equals(status)) {
      this.lastStatusOn = LocalDateTime.now(Clock.systemUTC());
    }
    this.status = status;
  }
}
