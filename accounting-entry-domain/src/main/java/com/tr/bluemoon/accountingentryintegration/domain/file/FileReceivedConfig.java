package com.tr.bluemoon.accountingentryintegration.domain.file;

import static com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitConfig.ACCOUNTING_ENTRY_INTEGRATION_EXCHANGE;
import static com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitConfig.ACCOUNTING_ENTRY_INTEGRATION_RETRY_EXCHANGE;

import com.tr.bluemoon.accountingentryintegration.domain.amqp.BindingDefinition;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.CloudEventMessageConverter;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.ExchangeDefinition;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.QueueDefinition;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitPostProcessor;
import com.tr.bluemoon.brtap.commons.domainawareness.RoutingPaths;
import java.util.concurrent.TimeUnit;
import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.inject.Produces;
import javax.inject.Named;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.RabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * File received queue configuration to handle {@link File} validation.
 *
 * <AUTHOR> Lanzendorf
 */
@Configuration
public class FileReceivedConfig {

  // Queues
  public static final String FILE_RECEIVED_QUEUE =
      "com.tr.bluemoon.subscriber.brtap.accountingentryintegration.file-received";
  public static final String FILE_RECEIVED_RETRY_QUEUE =
      "com.tr.bluemoon.subscriber.brtap.accountingentryintegration.file-received-retry";
  public static final String FILE_RECEIVED_DEAD_LETTER_QUEUE =
      "com.tr.bluemoon.subscriber.brtap.accountingentryintegration.file-received-dlq";

  // Events
  public static final String FILE_RECEIVED_EVENT =
      "com.tr.bluemoon.event.brtap.accountingentryintegration.file.v1.received";
  // Configs
  public static final long RETRY_MESSAGE_TTL = TimeUnit.MINUTES.toMillis(10);
  public static final long DEAD_LETTER_MESSAGE_TTL = TimeUnit.DAYS.toMillis(60);
  @Value("${brtap.accountingentryintegration.file-received.prefetch:1}")
  private Integer fileReceivedQueuePrefetch;

  // Declare Queues --------------------------------------------------------------------------------
  @ApplicationScoped
  @Produces
  @Bean
  public QueueDefinition fileReceivedQueue() {
    final var queue = new QueueDefinition(FILE_RECEIVED_QUEUE, true);
    queue.addArgument("x-dead-letter-exchange", ACCOUNTING_ENTRY_INTEGRATION_RETRY_EXCHANGE);
    return queue;
  }

  @ApplicationScoped
  @Produces
  @Bean
  public QueueDefinition fileReceivedRetryQueue() {
    final var queue = new QueueDefinition(FILE_RECEIVED_RETRY_QUEUE, true);
    queue.addArgument("x-message-ttl", RETRY_MESSAGE_TTL);
    queue.addArgument("x-dead-letter-exchange", ACCOUNTING_ENTRY_INTEGRATION_EXCHANGE);
    return queue;
  }

  @ApplicationScoped
  @Produces
  @Bean
  public QueueDefinition fileReceivedDeadLetterQueue() {
    final var queue = new QueueDefinition(FILE_RECEIVED_DEAD_LETTER_QUEUE, true);
    queue.addArgument("x-message-ttl", DEAD_LETTER_MESSAGE_TTL);
    return queue;
  }

  // Declare Bindings ------------------------------------------------------------------------------
  @ApplicationScoped
  @Produces
  @Bean
  public BindingDefinition fileReceivedAccountingEntryIntegrationCreateBindingPubSub(
      @Named("accountingEntryIntegrationExchange") @Qualifier("accountingEntryIntegrationExchange")
          ExchangeDefinition fileReceivedAccountingEntryIntegrationExchange) {
    final var queue = fileReceivedQueue();
    return new BindingDefinition(
        fileReceivedAccountingEntryIntegrationExchange, queue, FILE_RECEIVED_EVENT);
  }

  @ApplicationScoped
  @Produces
  @Bean
  public BindingDefinition fileReceivedAccountingEntryIntegrationCreateBindingRetry(
      @Named("accountingEntryIntegrationRetryExchange")
          @Qualifier("accountingEntryIntegrationRetryExchange")
          ExchangeDefinition fileReceivedAccountingEntryIntegrationRetryExchange) {
    final var queue = fileReceivedRetryQueue();
    return new BindingDefinition(
        fileReceivedAccountingEntryIntegrationRetryExchange, queue, FILE_RECEIVED_EVENT);
  }

  @ApplicationScoped
  @Produces
  @Bean
  public BindingDefinition fileReceivedAccountingEntryIntegrationCreateBindingDeadLetter(
      @Named("accountingEntryIntegrationDeadLetterExchange")
          @Qualifier("accountingEntryIntegrationDeadLetterExchange")
          ExchangeDefinition fileReceivedAccountingEntryIntegrationDeadLetterExchange) {
    final var queue = fileReceivedDeadLetterQueue();
    return new BindingDefinition(
        fileReceivedAccountingEntryIntegrationDeadLetterExchange, queue, FILE_RECEIVED_EVENT);
  }

  // Config for Spring Rabbit Listener -------------------------------------------------------------
  @Bean
  public RabbitListenerContainerFactory<SimpleMessageListenerContainer>
      fileReceivedQueueContainerFactory(
          ConnectionFactory rabbitConnectionFactory, RoutingPaths routingPaths) {
    final CloudEventMessageConverter converter = new CloudEventMessageConverter();

    SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
    factory.setConnectionFactory(rabbitConnectionFactory);
    factory.setPrefetchCount(fileReceivedQueuePrefetch);
    factory.setMessageConverter(converter);
    factory.setAfterReceivePostProcessors(new RabbitPostProcessor(converter, routingPaths));

    return factory;
  }
}
