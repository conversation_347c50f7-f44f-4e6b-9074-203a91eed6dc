package com.tr.bluemoon.accountingentryintegration.domain.file;

import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitMQEvent;
import java.util.UUID;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Event class to handle {@link File} validation.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
public class FileReceivedEvent implements RabbitMQEvent {

  private UUID id;

  public FileReceivedEvent(UUID id) {
    this.id = id;
  }
}
