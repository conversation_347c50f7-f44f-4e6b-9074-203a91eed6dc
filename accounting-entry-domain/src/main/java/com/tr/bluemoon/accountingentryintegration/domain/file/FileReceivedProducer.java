package com.tr.bluemoon.accountingentryintegration.domain.file;

import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitConfig;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitMQEnqueuer;
import com.tr.bluemoon.bracct.commons.UserInfo;
import javax.inject.Inject;

/**
 * Producer class to send {@link File} to received queue to be validated.
 *
 * <AUTHOR>
 */
public class FileReceivedProducer {

  private final RabbitMQEnqueuer enqueuer;
  private final UserInfo userInfo;

  @Inject
  public FileReceivedProducer(RabbitMQEnqueuer enqueuer, UserInfo userInfo) {
    this.enqueuer = enqueuer;
    this.userInfo = userInfo;
  }

  public void sendToValidate(FileReceivedEvent event) {
    enqueuer.publishMessage(
        event,
        FileReceivedConfig.FILE_RECEIVED_EVENT,
        RabbitConfig.ACCOUNTING_ENTRY_INTEGRATION_EXCHANGE,
        userInfo);
  }
}
