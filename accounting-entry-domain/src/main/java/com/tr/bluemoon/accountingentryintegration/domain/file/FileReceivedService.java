package com.tr.bluemoon.accountingentryintegration.domain.file;

import com.tr.bluemoon.accountingentryintegration.domain.aws.S3Service;
import com.tr.bluemoon.accountingentryintegration.domain.document.DocumentDeliveryProducer;
import com.tr.bluemoon.accountingentryintegration.domain.document.DocumentProducer;
import com.tr.bluemoon.accountingentryintegration.domain.document.DocumentService;
import com.tr.bluemoon.accountingentryintegration.domain.document.DocumentStatus;
import com.tr.bluemoon.accountingentryintegration.domain.xml.XmlAccountingEntryValidatorService;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.UUID;
import javax.ws.rs.InternalServerErrorException;
import javax.ws.rs.NotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Service class to handle {@link File} validation.
 *
 * <AUTHOR> Lanzendorf
 */
@Service
public class FileReceivedService {

  private static final Logger LOGGER = LoggerFactory.getLogger(FileReceivedService.class);

  private static final int VALIDATION_MAXIMUM_ATTEMPTS = 3;

  private final FileService fileService;
  private final DocumentService documentService;
  private final S3Service s3Service;
  private final XmlAccountingEntryValidatorService validator;
  private final DocumentDeliveryProducer documentDeliveryProducer;

  private final DocumentProducer documentProducer;

  @Autowired
  public FileReceivedService(FileService fileService, DocumentService documentService,
      S3Service s3Service, XmlAccountingEntryValidatorService validator,
      DocumentDeliveryProducer documentDeliveryProducer, DocumentProducer documentProducer) {
    this.fileService = fileService;
    this.documentService = documentService;
    this.s3Service = s3Service;
    this.validator = validator;
    this.documentDeliveryProducer = documentDeliveryProducer;
    this.documentProducer = documentProducer;
  }

  public void validate(UUID fileId) {
    File file = fileService.findById(fileId);

    if (file.getStatus().equals(FileStatus.VALIDATED)) {
      LOGGER.info("File already validated!");
      return;
    }

    try {
      file.setTotalValidationAttempts(file.getTotalValidationAttempts() + 1);

      InputStream content = s3Service.getXmlContentInputStream(file.getFilePath());
      if (content == null) {
        throw new NotFoundException();
      }

      validator.validateXml(content);

      var document = documentService.createDocument(fileId, file.getClientId(), file.getCompanyId(),
          file.getFilePath());

      if (file.isOnvio()) {
        documentProducer.publishDocument(document);
      } else {
        documentDeliveryProducer.sendToSocket(document);
      }
      document.setSentDate(LocalDateTime.now());
      document.setStatus(DocumentStatus.SENT);
      documentService.save(document);

      file.setStatus(FileStatus.VALIDATED);
    } catch (FileValidatorException e) {
      s3Service.deleteXml(file.getFilePath());
      file.setStatus(e.getStatus());
      file.setFilePath(null);

      final var detail = e.getError().getErrors().get(1);
      LOGGER.error("Error on validate file - " + e.getStatus() + " : " + detail.getMessage(), e);

    } catch (NotFoundException e) {
      file.setStatus(FileStatus.FILE_NOT_FOUND);
      LOGGER.error("Error on validate file - FILE NOT FOUND ON S3 FILE : " + e.getMessage(), e);

    } catch (Exception e) {
      LOGGER.error("Error on validate file - GENERIC ERROR - at attempt: "
          + file.getTotalValidationAttempts() + " - " + e.getMessage(), e);

      if (file.getTotalValidationAttempts() >= VALIDATION_MAXIMUM_ATTEMPTS) {
        file.setStatus(FileStatus.GENERIC_ERROR);
      }

      fileService.save(file);
      throw new InternalServerErrorException(e.getMessage(), e); // Throw to send to deadletter
    }

    fileService.save(file);
  }
}
