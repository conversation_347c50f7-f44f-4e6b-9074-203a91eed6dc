package com.tr.bluemoon.accountingentryintegration.domain.file;

import static com.tr.bluemoon.bracct.commons.jpa.query.QueryUtils.attribute;

import com.tr.bluemoon.bracct.commons.jpa.query.QueryBuilder;
import java.util.UUID;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import org.springframework.stereotype.Repository;

/**
 * Repository to handle persistence methods for {@link File} entity.
 *
 * <AUTHOR> Lanzendorf
 */
@Repository
public class FileRepository {

  @Inject @PersistenceContext private EntityManager entityManager;

  public File save(File entity) {
    if (entity.isNew()) {
      entityManager.persist(entity);

    } else {
      entity = entityManager.merge(entity);
    }

    return entity;
  }

  public File findById(UUID id) {
    return entityManager.find(File.class, id);
  }

  public File findByIdAndClientId(UUID id, UUID clientId) {
    TypedQuery<File> query =
        QueryBuilder.Factory.createQueryBuilder(entityManager, File.class)
            .from(File.class)
            .where(attribute("id").eq(id))
            .and(attribute("clientId").eq(clientId))
            .getQuery(entityManager);

    return query.getSingleResult();
  }
}
