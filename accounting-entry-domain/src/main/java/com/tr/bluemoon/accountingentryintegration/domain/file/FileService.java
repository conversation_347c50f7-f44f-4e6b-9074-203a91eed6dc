package com.tr.bluemoon.accountingentryintegration.domain.file;

import com.tr.bluemoon.accountingentryintegration.domain.aws.S3Service;
import com.tr.bluemoon.bracct.commons.UserInfo;
import com.tr.bluemoon.bracct.commons.util.UuidUtils;
import com.tr.bluemoon.commons.configuration.BlueMoonDomain;
import java.io.InputStream;
import java.util.Objects;
import java.util.UUID;
import javax.inject.Inject;
import javax.persistence.NoResultException;
import org.apache.deltaspike.jpa.api.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Service class to handle {@link File} business logic and coordinate persistence.
 *
 * <AUTHOR>
 */
@Service
public class FileService {

  private static final String FILE_FOLDER = "file";

  private final FileRepository repository;
  private final S3Service s3Service;
  private final UserInfo userInfo;

  @Inject
  public FileService(
      FileRepository repository,
      S3Service s3Service,
      @Autowired(required = false) UserInfo userInfo) {
    this.repository = repository;
    this.s3Service = s3Service;
    this.userInfo = userInfo;
  }

  @Transactional
  @org.springframework.transaction.annotation.Transactional
  public File save(File entity) {
    Objects.requireNonNull(entity, "entity cannot be null");
    return repository.save(entity);
  }

  @Transactional
  @org.springframework.transaction.annotation.Transactional
  public File save(File entity, InputStream is) {
    String filePath =
        s3Service.saveXml(
            is,
            FILE_FOLDER,
            entity.getClientId(),
            userInfo.getCompanyId(),
            BlueMoonDomain.getCurrent());

    entity.setFilePath(filePath);

    return save(entity);
  }

  @Transactional(readOnly = true)
  @org.springframework.transaction.annotation.Transactional(readOnly = true)
  public File findById(UUID id) {
    Objects.requireNonNull(id, "id cannot be null");
    return repository.findById(id);
  }

  @Transactional(readOnly = true)
  @org.springframework.transaction.annotation.Transactional(readOnly = true)
  public File findByIdAndClientId(String id, UUID clientId) {
    Objects.requireNonNull(id, "id cannot be null");
    Objects.requireNonNull(clientId, "clientId cannot be null");

    UUID fileId;
    try {
      fileId = UuidUtils.fromString(id);
    } catch (IllegalArgumentException e) {
      throw new InvalidProtocolException();
    }

    try {
      return repository.findByIdAndClientId(fileId, clientId);
    } catch (NoResultException e) {
      throw new ProtocolNotFoundException();
    }
  }
}
