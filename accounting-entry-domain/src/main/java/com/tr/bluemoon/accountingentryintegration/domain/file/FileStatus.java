package com.tr.bluemoon.accountingentryintegration.domain.file;

import static com.tr.bluemoon.accountingentryintegration.domain.file.FileStatusMessage.E0;
import static com.tr.bluemoon.accountingentryintegration.domain.file.FileStatusMessage.E2;
import static com.tr.bluemoon.accountingentryintegration.domain.file.FileStatusMessage.E3;
import static com.tr.bluemoon.accountingentryintegration.domain.file.FileStatusMessage.E7;
import static com.tr.bluemoon.accountingentryintegration.domain.file.FileStatusMessage.E8;
import static com.tr.bluemoon.accountingentryintegration.domain.file.FileStatusMessage.S1;
import static com.tr.bluemoon.accountingentryintegration.domain.file.FileStatusMessage.S2;

/**
 * {@link File} status Enum.
 *
 * <AUTHOR>
 */
public enum FileStatus implements IStatusContainer {

  // @formatter:off
  AWAITING_VALIDATION(S1),
  VALIDATED(S2),
  GENERIC_ERROR(E0),
  UNSUPPORTED_TYPE(E2),
  FILE_NOT_FOUND(E3),
  INVALID_PROTOCOL(E7),
  INVALID_STRUCTURE(E8);
  // @formatter:on

  private final IStatus status;

  FileStatus(IStatus status) {
    this.status = status;
  }

  @Override
  public IStatus getStatus() {
    return status;
  }
}
