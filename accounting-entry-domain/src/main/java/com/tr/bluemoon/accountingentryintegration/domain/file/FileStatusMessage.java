package com.tr.bluemoon.accountingentryintegration.domain.file;

/**
 * {@link File} status messages Enum.
 *
 * <AUTHOR>
 */
public enum FileStatusMessage implements IStatus {
  S1("Aguardando validação. Por favor aguarde o arquivo ser validado."),
  S2("Validado."),
  E0("Erro desconhecido."),
  E1("Protocolo não existe. Por favor consulte um protocolo existente."),
  E2("O arquivo recebido não é um XML. Por favor envie um arquivo no formato XML."),
  E3("Arquivo não encontrado."),
  E4("Cliente não habilitado ou inválido."),
  E5("O código hash identificador do parceiro é obrigatório."),
  E6("O código hash identificador do parceiro não está cadastrado."),
  E7(
      "Protocolo fora do padrão aceito pela API. Por favor consulte um protocolo dentro do padrão aceito pela API."),
  E8("Estrutura inválida. Envie um arquivo com uma estrutura aceita pela API.");

  private final String message;

  FileStatusMessage(String message) {
    this.message = message;
  }

  public String getCode() {
    return this.name();
  }

  public String getMessage() {
    return this.message;
  }
}
