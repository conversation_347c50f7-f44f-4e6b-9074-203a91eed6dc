package com.tr.bluemoon.accountingentryintegration.domain.file;

import com.tr.bluemoon.bracct.commons.exception.Error;
import com.tr.bluemoon.bracct.commons.exception.Error.Detail;

/**
 * Interface Status.
 *
 * <AUTHOR>
 */
public interface IStatus {

  public String getCode();

  public String getMessage();

  public default Error getError() {
    Error error = new Error();
    error.addDetail(new Detail(getCode(), getMessage(), null));
    return error;
  }
}
