package com.tr.bluemoon.accountingentryintegration.domain.file;

import com.tr.bluemoon.bracct.commons.exception.NotFoundEntryException;

public class InvalidProtocolException extends NotFoundEntryException {

  private static final long serialVersionUID = 1L;

  public InvalidProtocolException() {
    super(FileStatusMessage.E7.getError());
    this.getError().setCode(404);
    this.getError().setMessage("Invalid Protocol Format");
  }
}
