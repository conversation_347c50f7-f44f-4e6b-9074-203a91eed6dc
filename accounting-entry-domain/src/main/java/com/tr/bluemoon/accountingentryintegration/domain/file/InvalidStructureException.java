package com.tr.bluemoon.accountingentryintegration.domain.file;

import com.tr.bluemoon.bracct.commons.exception.Error.Detail;

public class InvalidStructureException extends FileValidatorException {

  private static final long serialVersionUID = 1L;

  public InvalidStructureException(String message) {
    super(FileStatus.INVALID_STRUCTURE);
    this.getError().addDetail(new Detail(message, null));
  }
}
