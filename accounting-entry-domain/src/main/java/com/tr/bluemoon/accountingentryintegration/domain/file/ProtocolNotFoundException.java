package com.tr.bluemoon.accountingentryintegration.domain.file;

import com.tr.bluemoon.bracct.commons.exception.NotFoundEntryException;

public class ProtocolNotFoundException extends NotFoundEntryException {

  private static final long serialVersionUID = 1L;

  public ProtocolNotFoundException() {
    super(FileStatusMessage.E1.getError());
    this.getError().setCode(404);
    this.getError().setMessage("Protocol Not Found");
  }
}
