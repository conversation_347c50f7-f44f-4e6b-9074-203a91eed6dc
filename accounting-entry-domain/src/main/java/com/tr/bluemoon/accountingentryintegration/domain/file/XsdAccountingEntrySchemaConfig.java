package com.tr.bluemoon.accountingentryintegration.domain.file;

import static javax.xml.XMLConstants.ACCESS_EXTERNAL_DTD;
import static javax.xml.XMLConstants.ACCESS_EXTERNAL_SCHEMA;
import static javax.xml.XMLConstants.W3C_XML_SCHEMA_NS_URI;

import com.tr.bluemoon.bracct.commons.exception.BusinessException;
import javax.xml.transform.Source;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.xml.sax.SAXException;

@Configuration
class XsdAccountingEntrySchemaConfig {

  @Value("${br.accountingentry.xsd:/xml/accounting-entry.xsd}")
  String xsd;

  @Bean
  Schema schema() {
    try {
      Source schemaFile = new StreamSource(getClass().getResourceAsStream(xsd));
      SchemaFactory factory = SchemaFactory.newInstance(W3C_XML_SCHEMA_NS_URI);
      factory.setProperty(ACCESS_EXTERNAL_SCHEMA, "");
      factory.setProperty(ACCESS_EXTERNAL_DTD, "");
      return factory.newSchema(schemaFile);
    } catch (SAXException e) {
      throw new BusinessException("Fail to load XSD file", e);
    }
  }
}
