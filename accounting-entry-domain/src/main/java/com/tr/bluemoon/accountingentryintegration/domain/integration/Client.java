package com.tr.bluemoon.accountingentryintegration.domain.integration;

import com.tr.bluemoon.accountingentryintegration.domain.database.CompatibleAuditableListener;
import com.tr.bluemoon.bracct.commons.jpa.converter.LocalDateTimeToDateConverter;
import com.tr.bluemoon.bracct.commons.jpa.converter.UuidToUuidConverter;
import com.tr.bluemoon.bracct.commons.jpa.model.CompanyOwnershipEntity;
import java.time.LocalDateTime;
import java.util.UUID;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.ExcludeSuperclassListeners;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * Entity to handle clients data.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "client")
@ExcludeSuperclassListeners // Auditable from the parent is very coupled with UserInfo and CDI
@EntityListeners(value = {CompatibleAuditableListener.class})
// Makes auditable compatible with both CDI and RabbitPostProcessor
public class Client extends CompanyOwnershipEntity<UUID> {

  private static final long serialVersionUID = 1L;

  @Id
  @Convert(converter = UuidToUuidConverter.class)
  @Column(name = "client_id", nullable = false, insertable = true, updatable = false)
  private UUID id;

  @NotNull
  @Column(name = "is_enabled")
  private boolean enabled;

  @NotBlank
  @Column(name = "code")
  private String code;

  @NotBlank
  @Column(name = "name")
  private String name;

  @Column(name = "national_identity")
  private String nationalIdentity;

  @Column(name = "national_identity_type")
  private String nationalIdentityType;

  @Column(name = "first_enable_on")
  @Convert(converter = LocalDateTimeToDateConverter.class)
  private LocalDateTime firstEnableOn;

  @Column(name = "last_enable_on")
  @Convert(converter = LocalDateTimeToDateConverter.class)
  private LocalDateTime lastEnableOn;

  @Column(name = "email_subject")
  private String emailSubject;

  @Column(name = "email_content")
  private String emailContent;

  @Column(name = "message_synchronize_date")
  private LocalDateTime synchronizeDate;

  @Column(name = "is_onvio")
  private boolean isOnvio = false;

  public Client() {
    enabled = false;
  }
}
