package com.tr.bluemoon.accountingentryintegration.domain.integration;

import com.tr.bluemoon.bracct.commons.jpa.UUIDGenerator;
import com.tr.bluemoon.bracct.commons.jpa.converter.LocalDateTimeToDateConverter;
import com.tr.bluemoon.bracct.commons.jpa.converter.UuidToUuidConverter;
import com.tr.bluemoon.bracct.commons.jpa.model.CompanyOwnershipEntity;
import java.time.LocalDateTime;
import java.util.UUID;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "client_api_integration")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class ClientApiIntegration extends CompanyOwnershipEntity<UUID> {

  private static final long serialVersionUID = 1L;

  @Id
  @GeneratedValue(generator = UUIDGenerator.UUID_GENERATOR)
  @Convert(converter = UuidToUuidConverter.class)
  @Column(
      name = "client_api_integration_id",
      nullable = false,
      insertable = true,
      updatable = false)
  private UUID id;

  @NotBlank
  @Column(name = "activation_key", length = 50)
  private String activationKey;

  @Column(name = "integration_key", length = 250)
  private String integrationKey;

  @Column(name = "partner_client_id", length = 250)
  private String partnerClientId;

  @Column(name = "description", length = 250)
  private String description;

  @Column(name = "activation_date")
  @Convert(converter = LocalDateTimeToDateConverter.class)
  private LocalDateTime activation;

  @NotNull
  @Column(name = "client_id", insertable = true, updatable = true)
  @Convert(converter = UuidToUuidConverter.class)
  private UUID clientId;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(
      name = "client_id",
      referencedColumnName = "client_id",
      insertable = false,
      updatable = false)
  private Client client;
}
