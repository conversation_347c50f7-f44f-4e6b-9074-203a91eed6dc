package com.tr.bluemoon.accountingentryintegration.domain.integration;

import static com.tr.bluemoon.bracct.commons.jpa.query.QueryUtils.attribute;

import com.tr.bluemoon.accountingentryintegration.domain.database.InvoicingBucketableDatabase;
import com.tr.bluemoon.bracct.commons.UserInfo;
import com.tr.bluemoon.bracct.commons.jpa.BaseDao;
import com.tr.bluemoon.bracct.commons.jpa.query.QueryBuilder;
import java.util.UUID;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.TypedQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Data Access Object to handle persistence methods for {@link ClientApiIntegration} entity.
 *
 * <AUTHOR> Lanzendorf
 */
public class ClientApiIntegrationDao extends BaseDao<ClientApiIntegration, UUID> {

  private static final Logger LOGGER = LoggerFactory.getLogger(ClientApiIntegrationDao.class);

  private final UserInfo userInfo;

  @Inject
  public ClientApiIntegrationDao(
      @InvoicingBucketableDatabase EntityManager entityManager,
      UserInfo userInfo) {
    super(entityManager);
    this.userInfo = userInfo;
  }

  /**
   * Find {@link ClientApiIntegration} by integration key.
   *
   * @param integrationKey integration key
   * @return ClientApiIntegration
   */
  public ClientApiIntegration findClientApiIntegrationByIntegrationKey(String integrationKey) {
    try {
      TypedQuery<ClientApiIntegration> query = QueryBuilder.Factory.createQueryBuilder(
              getEntityManager(), ClientApiIntegration.class)
          .from(ClientApiIntegration.class, "cai")
          .where(attribute("cai.integrationKey").eq(integrationKey))
          .getQuery(getEntityManager());
      return query.getSingleResult();
    } catch (NoResultException ex) {
      LOGGER.debug("Integration key does not exists.", ex);
      return null;
    }
  }

  public String getCacheKey(String key) {
    // Use of Invoice key here is intentional, we can reuse the same key in Redis
    String cacheKey = "BRInvoiceIntegrationServices:%s:clientApiIntegration:IntegrationKey:%s";
    return String.format(cacheKey, userInfo.getCompanyId(), key);
  }
}
