package com.tr.bluemoon.accountingentryintegration.domain.integration;

import java.util.Objects;
import javax.inject.Inject;
import javax.persistence.NoResultException;
import org.apache.deltaspike.jpa.api.transaction.Transactional;

/**
 * Service class to handle {@link ClientApiIntegration}. business logic and coordinate persistence.
 *
 * <AUTHOR>
 */
@Transactional(readOnly = true)
public class ClientApiIntegrationService {

  private final ClientApiIntegrationDao dao;

  @Inject
  public ClientApiIntegrationService(ClientApiIntegrationDao dao) {
    this.dao = dao;
  }

  /**
   * Find a {@link ClientApiIntegration} by integration key.
   *
   * @param integrationKey of the client
   * @return the found {@link ClientApiIntegration} or {@link NoResultException} if no one was
   *     found.
   */
  public ClientApiIntegration findClientApiIntegrationByIntegrationKey(String integrationKey) {
    Objects.requireNonNull(integrationKey, "integration key cannot be null");
    return dao.findClientApiIntegrationByIntegrationKey(integrationKey);
  }
}
