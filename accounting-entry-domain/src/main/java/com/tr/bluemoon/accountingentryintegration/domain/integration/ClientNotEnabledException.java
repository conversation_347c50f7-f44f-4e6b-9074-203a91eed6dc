package com.tr.bluemoon.accountingentryintegration.domain.integration;

import com.tr.bluemoon.accountingentryintegration.domain.file.FileStatusMessage;
import com.tr.bluemoon.bracct.commons.exception.BusinessException;

public class ClientNotEnabledException extends BusinessException {

  private static final long serialVersionUID = 1L;

  public ClientNotEnabledException() {
    super(FileStatusMessage.E4.getError());
    this.getError().setCode(400);
    this.getError().setMessage("Client Not Enabled");
  }
}
