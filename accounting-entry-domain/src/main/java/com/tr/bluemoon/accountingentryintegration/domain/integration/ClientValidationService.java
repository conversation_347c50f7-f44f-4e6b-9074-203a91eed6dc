package com.tr.bluemoon.accountingentryintegration.domain.integration;

import com.tr.bluemoon.bracct.commons.util.UuidUtils;
import javax.ws.rs.ForbiddenException;

/**
 * Service class to handle client validation.
 *
 * <AUTHOR>
 */
public class ClientValidationService {

  /**
   * Check client id.
   *
   * @param clientId client id
   */
  public void checkClientId(String clientId) {
    if (!isValidUuid(clientId)) {
      throw new ClientNotEnabledException();
    }
  }

  /**
   * Check whether client id is a valid UUID.
   *
   * @param clientId client id
   * @return whether client id is a valid UUID
   */
  protected boolean isValidUuid(String clientId) {
    try {
      UuidUtils.fromString(clientId);
      return clientId != null;
    } catch (Exception ex) {
      return false;
    }
  }

  /**
   * Validate if the {@link Client} exists.
   *
   * @param client {@link Client} to be validated
   */
  public void validateClientExists(Client client) {
    if (client == null) {
      throw new ClientNotEnabledException();
    }
  }

  /**
   * Validate {@link ClientApiIntegration}.
   *
   * @param client {@link ClientApiIntegration} to be validated
   * @param partnerClientId partner client id
   */
  public void validate(ClientApiIntegration client, String partnerClientId) {
    if (client == null
        || client.getClientId() == null
        || UuidUtils.EMPTY_UUID == client.getClientId()) {
      throw new ClientNotEnabledException();
    }

    if (!partnerClientId.equals(client.getPartnerClientId())) {
      throw new ForbiddenException();
    }
  }
}
