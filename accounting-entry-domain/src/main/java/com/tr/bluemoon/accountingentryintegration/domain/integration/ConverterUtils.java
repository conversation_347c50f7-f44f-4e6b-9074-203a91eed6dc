package com.tr.bluemoon.accountingentryintegration.domain.integration;

import com.tr.bluemoon.bracct.commons.exception.BusinessException;
import java.nio.ByteBuffer;
import java.util.Base64;
import java.util.UUID;

public class ConverterUtils {

  private ConverterUtils() {
    // Util methods are all static
  }

  /**
   * Encode to base 64.
   *
   * @param content contemt as byte array
   * @return base 64 encoded string
   */
  public static String base64Encode(byte[] content) {
    String s = Base64.getEncoder().encodeToString(content); // Regular base64 encoder
    s = s.split("=")[0]; // Remove any trailing '='s
    s = s.replace('+', '-'); // 62nd char of encoding
    s = s.replace('/', '_'); // 63rd char of encoding
    return s;
  }

  /**
   * Decode from base 64.
   *
   * @param text base 64 encoded text
   * @return byte array from the base 64
   * @throws Exception when text not encoded
   */
  public static byte[] base64Decode(String text) throws Exception {
    String s = text;
    s = s.replace('-', '+');
    s = s.replace('_', '/');
    switch (s.length() % 4) {
      case 0:
        break;
      case 2:
        s += "==";
        break;
      case 3:
        s += "=";
        break;
      default:
        throw new BusinessException("Illegal base64url string!");
    }
    return Base64.getDecoder().decode(s);
  }

  /**
   * Transform byte array to uuid.
   *
   * @param bytes uuid as byte array
   * @return uuid
   */
  public static UUID asUuid(byte[] bytes) {
    ByteBuffer bb = ByteBuffer.wrap(bytes);
    long firstLong = bb.getLong();
    long secondLong = bb.getLong();
    return new UUID(firstLong, secondLong);
  }

  /**
   * Transform uuid to byte array.
   *
   * @param uuid uuid
   * @return byte array
   */
  public static byte[] asBytes(UUID uuid) {
    ByteBuffer bb = ByteBuffer.wrap(new byte[16]);
    bb.putLong(uuid.getMostSignificantBits());
    bb.putLong(uuid.getLeastSignificantBits());
    return bb.array();
  }
}
