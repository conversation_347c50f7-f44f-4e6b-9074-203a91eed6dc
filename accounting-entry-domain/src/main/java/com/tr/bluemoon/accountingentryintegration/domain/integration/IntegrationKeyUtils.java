package com.tr.bluemoon.accountingentryintegration.domain.integration;

import com.tr.bluemoon.bracct.commons.exception.BusinessException;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.UUID;
import java.util.regex.Pattern;

public abstract class IntegrationKeyUtils {

  private static final String UUID_PATTERN =
      "([0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12})";

  private IntegrationKeyUtils() {
    // Util methods are all static
  }

  /**
   * Generate integration key encoded from uuid.
   *
   * @param uuid uuid to generate the key
   * @return String integration key encoded
   * @throws IOException when failed to encode
   */
  public static String uuidEncode(UUID uuid) throws IOException {
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    outputStream.write(ConverterUtils.asBytes(uuid));
    outputStream.write(ConverterUtils.asBytes(UUID.randomUUID()));
    return ConverterUtils.base64Encode(outputStream.toByteArray());
  }

  /**
   * Decode activation key.
   *
   * @param header activation key from request header
   * @return UUID uuid included in header
   * @throws Exception when failed to decode
   */
  public static UUID uuidDecode(String header) throws Exception {
    if (header.length() != 43) {
      throw new BusinessException("Invalid Key supplied");
    }

    byte[] decoded = ConverterUtils.base64Decode(header.substring(0, 22));
    UUID uuidDecoded = ConverterUtils.asUuid(decoded);

    validateUUID(uuidDecoded);

    return uuidDecoded;
  }

  protected static void validateUUID(UUID uuid) {
    Pattern pattern = Pattern.compile(UUID_PATTERN);
    if (!pattern.matcher(uuid.toString()).matches()) {
      throw new BusinessException("Invalid Key supplied");
    }
  }
}
