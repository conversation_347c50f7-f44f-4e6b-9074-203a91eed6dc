package com.tr.bluemoon.accountingentryintegration.domain.integration;

import com.tr.bluemoon.accountingentryintegration.domain.file.FileStatusMessage;
import com.tr.bluemoon.bracct.commons.exception.BusinessException;

public class PartnerNotEnabledException extends BusinessException {

  private static final long serialVersionUID = 1L;

  public PartnerNotEnabledException() {
    super(FileStatusMessage.E6.getError());
    this.getError().setCode(400);
    this.getError().setMessage("Partner Not Enabled");
  }
}
