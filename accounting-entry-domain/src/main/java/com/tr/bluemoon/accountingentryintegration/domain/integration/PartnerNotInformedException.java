package com.tr.bluemoon.accountingentryintegration.domain.integration;

import com.tr.bluemoon.accountingentryintegration.domain.file.FileStatusMessage;
import com.tr.bluemoon.bracct.commons.exception.BusinessException;

public class PartnerNotInformedException extends BusinessException {

  private static final long serialVersionUID = 1L;

  public PartnerNotInformedException() {
    super(FileStatusMessage.E5.getError());
    this.getError().setCode(400);
    this.getError().setMessage("Partner Not Informed");
  }
}
