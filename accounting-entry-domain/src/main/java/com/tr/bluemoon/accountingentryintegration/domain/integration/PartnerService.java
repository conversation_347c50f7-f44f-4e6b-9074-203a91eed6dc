package com.tr.bluemoon.accountingentryintegration.domain.integration;

import com.tr.bluemoon.bracct.commons.UserInfo;
import com.tr.bluemoon.bracct.commons.util.UuidUtils;
import javax.inject.Inject;

/**
 * Service class to handle partners business logic and coordinate persistence.
 *
 * <AUTHOR>
 */
public class PartnerService {

  private final UserInfo userInfo;

  @Inject
  public PartnerService(UserInfo userInfo) {
    this.userInfo = userInfo;
  }

  /**
   * Check whether partner is enabled.
   *
   * @param partnerId partner id
   * @return whether partner is enabled
   */
  public static boolean isPartnerEnabled(String partnerId) {
    // TODO: create dynamic validation for future partners
    return UuidUtils.fromString("edd2b872-bc4c-4ed8-ac38-0628d55d55fe")
        .equals(UuidUtils.fromString(partnerId));
  }

  /**
   * Check partner id.
   *
   * @param partnerId partner id
   */
  public void checkPartnerId(String partnerId) {
    if (userInfo.isStaffEnabled()) {
      if (!isValidUuid(partnerId)) {
        throw new PartnerNotInformedException();
      } else {
        if (!isPartnerEnabled(partnerId)) {
          throw new PartnerNotEnabledException();
        }
      }
    }
  }

  /**
   * Check whether partner id is a valid UUID.
   *
   * @param partnerId partner id
   * @return whether partner id is a valid UUID
   */
  protected boolean isValidUuid(String partnerId) {
    try {
      UuidUtils.fromString(partnerId);
      return partnerId != null;
    } catch (Exception ex) {
      return false;
    }
  }
}
