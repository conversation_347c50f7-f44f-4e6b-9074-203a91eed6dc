package com.tr.bluemoon.accountingentryintegration.domain.outboxevent;

import java.time.LocalDateTime;
import java.util.UUID;
import javax.persistence.Column;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.annotation.Version;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "outbox_event")
public class OutboxEvent {

  @Id
  @Column(name = "outbox_event_id")
  private Long id;

  private String source;

  @Column(name = "chain_id")
  private UUID chainId;

  @Column(name = "company_id")
  private UUID companyId;

  @Column(name = "contact_id")
  private UUID contactId;

  @Column(name = "event_type")
  private String eventType;

  @Column(name = "event_id")
  private UUID eventId;

  private String topic;

  @Column(name = "partition_key")
  private String partitionKey;

  private String payload;

  @CreatedDate
  @Column(name = "create_date")
  private LocalDateTime created;

  @Column(name = "created_by__contact_id")
  private UUID createdBy;

  @LastModifiedDate
  @Column(name = "changed")
  private LocalDateTime changed;

  @Column(name = "changed_by__contact_id")
  private UUID changedBy;

  @Version
  private Long version;

}
