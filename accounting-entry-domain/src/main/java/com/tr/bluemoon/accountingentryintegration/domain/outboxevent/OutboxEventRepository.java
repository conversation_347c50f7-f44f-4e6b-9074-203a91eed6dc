package com.tr.bluemoon.accountingentryintegration.domain.outboxevent;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import org.springframework.stereotype.Repository;

@Repository
public class OutboxEventRepository {

  @Inject
  @PersistenceContext
  private EntityManager entityManager;

  OutboxEvent save(OutboxEvent entity) {
    if (entity.getId() == null) {
      entityManager.persist(entity);

    } else {
      entity = entityManager.merge(entity);
    }

    return entity;
  }
}
