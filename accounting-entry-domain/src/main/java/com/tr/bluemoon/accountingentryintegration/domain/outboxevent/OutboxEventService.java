package com.tr.bluemoon.accountingentryintegration.domain.outboxevent;

import org.springframework.stereotype.Service;

@Service
public class OutboxEventService {

  private final OutboxEventRepository outboxEventRepository;

  public OutboxEventService(OutboxEventRepository outboxEventRepository) {
    this.outboxEventRepository = outboxEventRepository;
  }

  public OutboxEvent save(OutboxEvent outboxEvent) {
    return outboxEventRepository.save(outboxEvent);
  }

}