package com.tr.bluemoon.accountingentryintegration.domain.rehydration;

import com.tr.bluemoon.accountingentryintegration.domain.database.CompatibleAuditableListener;
import com.tr.bluemoon.bracct.commons.jpa.UUIDGenerator;
import com.tr.bluemoon.bracct.commons.jpa.converter.LocalDateTimeToDateConverter;
import com.tr.bluemoon.bracct.commons.jpa.converter.UuidToUuidConverter;
import com.tr.bluemoon.bracct.commons.jpa.model.CompanyOwnershipEntity;
import java.time.Clock;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.ExcludeSuperclassListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "rehydration")
@ExcludeSuperclassListeners // Auditable from the parent is very coupled with UserInfo and CDI
@EntityListeners(value = {CompatibleAuditableListener.class})
// Makes auditable compatible with both CDI and RabbitPostProcessor
public class Rehydration extends CompanyOwnershipEntity<UUID> {

  @Id
  @GeneratedValue(generator = UUIDGenerator.UUID_GENERATOR)
  @Convert(converter = UuidToUuidConverter.class)
  @Column(name = "rehydration_id", nullable = false, updatable = false)
  private UUID id;

  @NotNull
  @Column(name = "offset_date")
  @Convert(converter = LocalDateTimeToDateConverter.class)
  private LocalDateTime offsetDate;

  @NotNull
  @Column(name = "status")
  @Enumerated(EnumType.STRING)
  private RehydrationStatus status;

  @Column(name = "last_status_on")
  @Convert(converter = LocalDateTimeToDateConverter.class)
  private LocalDateTime lastStatusOn;

  @Column(name = "sent_date")
  @Convert(converter = LocalDateTimeToDateConverter.class)
  private LocalDateTime sentDate;

  @OneToMany(mappedBy = "rehydration")
  private List<RehydrationDocument> documents;

  @Override
  public UUID getId() {
    return this.id;
  }

  @Override
  public void setId(UUID id) {
    this.id = id;
  }

  public Rehydration() {
    this.status = RehydrationStatus.AWAITING_TO_SEND;
    this.lastStatusOn = LocalDateTime.now(Clock.systemUTC());
  }

  public void setStatus(RehydrationStatus status) {
    if (!this.status.equals(status)) {
      this.lastStatusOn = LocalDateTime.now(Clock.systemUTC());
    }
    this.status = status;
  }
}
