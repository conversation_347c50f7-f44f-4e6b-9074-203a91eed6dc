package com.tr.bluemoon.accountingentryintegration.domain.rehydration;

import com.tr.bluemoon.accountingentryintegration.domain.database.CompatibleAuditableListener;
import com.tr.bluemoon.bracct.commons.jpa.UUIDGenerator;
import com.tr.bluemoon.bracct.commons.jpa.converter.UuidToUuidConverter;
import com.tr.bluemoon.bracct.commons.jpa.model.CompanyOwnershipEntity;
import java.util.UUID;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.ExcludeSuperclassListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "rehydration_document")
@ExcludeSuperclassListeners // Auditable from the parent is very coupled with UserInfo and CDI
@EntityListeners(value = {CompatibleAuditableListener.class})
// Makes auditable compatible with both CDI and RabbitPostProcessor
public class RehydrationDocument extends CompanyOwnershipEntity<UUID> {

  @Id
  @GeneratedValue(generator = UUIDGenerator.UUID_GENERATOR)
  @Convert(converter = UuidToUuidConverter.class)
  @Column(name = "rehydration_document_id", nullable = false, updatable = false)
  private UUID id;

  @NotNull
  @Convert(converter = UuidToUuidConverter.class)
  @Column(name = "rehydration_id", nullable = false, updatable = false)
  private UUID rehydrationId;

  @NotNull
  @Column(name = "document_id")
  @Convert(converter = UuidToUuidConverter.class)
  private UUID documentId;

  @NotNull
  @Column(name = "client_id")
  @Convert(converter = UuidToUuidConverter.class)
  private UUID clientId;

  @ManyToOne
  @JoinColumn(name = "rehydration_id", nullable = false, updatable = false, insertable = false)
  private Rehydration rehydration;

}
