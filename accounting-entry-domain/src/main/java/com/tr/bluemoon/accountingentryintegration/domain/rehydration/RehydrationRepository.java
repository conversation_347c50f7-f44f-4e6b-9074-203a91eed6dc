package com.tr.bluemoon.accountingentryintegration.domain.rehydration;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import org.springframework.stereotype.Repository;

@Repository
public class RehydrationRepository {

  @Inject
  @PersistenceContext
  private EntityManager entityManager;

  public Rehydration save(Rehydration entity) {
    if (entity.isNew()) {
      entityManager.persist(entity);

    } else {
      entity = entityManager.merge(entity);
    }

    return entity;
  }
}
