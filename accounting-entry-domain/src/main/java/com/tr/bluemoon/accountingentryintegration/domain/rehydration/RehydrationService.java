package com.tr.bluemoon.accountingentryintegration.domain.rehydration;

import com.tr.bluemoon.accountingentryintegration.domain.document.Document;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.deltaspike.jpa.api.transaction.Transactional;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class RehydrationService {

  private final RehydrationRepository repository;

  @Transactional
  @org.springframework.transaction.annotation.Transactional
  public Rehydration create(List<Document> documents, LocalDateTime offset) {
    final Rehydration rehydration = new Rehydration();
    rehydration.setId(UUID.randomUUID());
    rehydration.setOffsetDate(offset);

    final List<RehydrationDocument> rehydrationDocuments = documents.stream()
        .map(document -> {
          RehydrationDocument rehydrationDocument = new RehydrationDocument();
          rehydrationDocument.setRehydrationId(rehydration.getId());
          rehydrationDocument.setDocumentId(document.getId());
          rehydrationDocument.setClientId(document.getClientId());

          return rehydrationDocument;
        }).collect(Collectors.toUnmodifiableList());

    rehydration.setDocuments(rehydrationDocuments);
    return this.save(rehydration);
  }

  @Transactional
  @org.springframework.transaction.annotation.Transactional
  public Rehydration save(Rehydration rehydration) {
    return repository.save(rehydration);
  }
}
