package com.tr.bluemoon.accountingentryintegration.domain.synchronization;

import com.tr.bluemoon.accountingentryintegration.domain.synchronization.dto.ClientV3Dto;
import com.tr.bluemoon.accountingentryintegration.domain.synchronization.dto.FilterSearchSortPost;
import com.tr.bluemoon.accountingentryintegration.domain.synchronization.dto.PagedClientsV3Dto;
import com.tr.bluemoon.accountingentryintegration.domain.synchronization.dto.SearchDto;
import com.tr.bluemoon.bracct.commons.UserInfo;
import com.tr.bluemoon.bracct.commons.util.UuidUtils;
import com.tr.bluemoon.commons.token.authentication.BluemoonCompanyServicesTokenV2;
import com.tr.bluemoon.commons.utils.RoutingKeys;
import com.tr.bluemoon.core.CoreServicesException;
import com.tr.bluemoon.core.CoreServicesRestClient;
import com.tr.bluemoon.domainawareness.environment.Environments;
import java.net.URI;
import java.util.UUID;
import javax.inject.Inject;
import javax.persistence.NoResultException;
import javax.ws.rs.core.UriBuilder;

public class ClientCoreService {

  private static final String CLIENT_NOT_FOUND_MESSAGE = "Client not found.";
  private static final String CORE_CONTACT_EXPANDED =
      "primaryContactExpanded,primaryContactExpanded.nationalIdentitiesExpanded,primaryContactExpanded.contactDataExpanded,clientMainExpanded";
  private static final String CORE_FILTER_BY_ID_OP_EQ =
      "{'items':[{'by':'id','op':'EQ','value':'%s'}]}";
  private final UserInfo userInfo;

  @Inject
  public ClientCoreService(UserInfo userInfo) {
    this.userInfo = userInfo;
  }

  public PagedClientsV3Dto get(SearchDto body, UUID companyId) throws CoreServicesException {
    String companyIdAsString = UuidUtils.fromUUID(companyId);
    UriBuilder builder =
        UriBuilder.fromPath(getCoreServicesRoute())
            .path("{version}")
            .path("companies")
            .path("{companyId}")
            .path("clients")
            .path("search");
    URI uri = builder.build("v3", companyIdAsString);

    return postToCoreServices(body, companyIdAsString, uri);
  }

  public ClientV3Dto get(String id) throws NoResultException, CoreServicesException {
    SearchDto searchDto = getFilterSearchPost(String.format(CORE_FILTER_BY_ID_OP_EQ, id));

    PagedClientsV3Dto paged = get(searchDto, userInfo.getCompanyId());

    if (paged.getCurrentItemCount() == 0) {
      throw new NoResultException(CLIENT_NOT_FOUND_MESSAGE);
    }

    return paged.getItems().get(0);
  }

  protected String getCoreServicesRoute() {
    return Environments.get().getRoutingTable().get(RoutingKeys.CoreServices.key());
  }

  protected PagedClientsV3Dto postToCoreServices(SearchDto body, String companyId, URI uri)
      throws CoreServicesException {
    BluemoonCompanyServicesTokenV2 bmToken =
        BluemoonCompanyServicesTokenV2.createForCompany(companyId);
    CoreServicesRestClient<Object, PagedClientsV3Dto> rest =
        new CoreServicesRestClient<>(uri, bmToken);

    Class<PagedClientsV3Dto> clazz = PagedClientsV3Dto.class;
    return rest.post(body, clazz);
  }

  private SearchDto getFilterSearchPost(String filter) {
    FilterSearchSortPost filterSearchSortPost = new FilterSearchSortPost();
    filterSearchSortPost.setFilter(filter);

    SearchDto searchDto = new SearchDto();
    searchDto.setExcludeCount(Boolean.TRUE);
    searchDto.setExpand(CORE_CONTACT_EXPANDED);
    searchDto.setFilterSearchSort(filterSearchSortPost);

    return searchDto;
  }
}
