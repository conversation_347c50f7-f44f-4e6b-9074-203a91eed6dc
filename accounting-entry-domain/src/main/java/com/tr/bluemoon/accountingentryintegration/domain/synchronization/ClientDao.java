package com.tr.bluemoon.accountingentryintegration.domain.synchronization;

import com.fasterxml.jackson.core.type.TypeReference;
import com.tr.bluemoon.accountingentryintegration.domain.cache.CacheBaseDao;
import com.tr.bluemoon.accountingentryintegration.domain.database.InvoicingBucketableDatabase;
import com.tr.bluemoon.accountingentryintegration.domain.integration.Client;
import com.tr.bluemoon.bracct.commons.UserInfo;
import java.util.UUID;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;

/**
 * Data Access Object to handle persistence methods for {@link Client} entity.
 *
 * <AUTHOR>
 */
public class ClientDao extends CacheBaseDao<Client, UUID> {

  private final UserInfo userInfo;

  @Inject
  public ClientDao(@InvoicingBucketableDatabase EntityManager entityManager, UserInfo userInfo) {
    super(entityManager);
    this.userInfo = userInfo;
  }

  /**
   * Find a client by its id.
   *
   * @param id entity id
   * @return entity
   * @throws NoResultException if there is no result
   */
  @Override
  public Client findById(UUID id) {
    return get(id, new TypeReference<>() {}, () -> super.findById(id));
  }

  @Override
  public String getCacheKey(UUID id) {
    String key = "BRInvoiceIntegrationServices:%s:client:entity:%s";
    return String.format(key, userInfo.getCompanyId(), id);
  }
}
