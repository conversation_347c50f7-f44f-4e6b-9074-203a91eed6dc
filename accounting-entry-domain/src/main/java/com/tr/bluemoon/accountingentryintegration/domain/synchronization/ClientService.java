package com.tr.bluemoon.accountingentryintegration.domain.synchronization;

import com.tr.bluemoon.accountingentryintegration.domain.integration.Client;
import com.tr.bluemoon.bracct.commons.util.UuidUtils;
import java.util.Objects;
import java.util.UUID;
import javax.inject.Inject;
import javax.persistence.NoResultException;
import org.apache.deltaspike.jpa.api.transaction.Transactional;

/**
 * Service class to handle {@link Client} business logic and coordinate persistence.
 *
 * <AUTHOR>
 */
@Transactional(readOnly = true)
public class ClientService {

  private final ClientDao dao;

  @Inject
  public ClientService(ClientDao dao) {
    this.dao = dao;
  }

  /**
   * Find a {@link Client} by id.
   *
   * @param id Batch id
   * @return the found {@link Client} or {@link NoResultException} if no one was found.
   */
  public Client findById(String id) {
    requireIdNonNull(id);
    return findById(UuidUtils.fromString(id));
  }

  /**
   * Find a {@link Client} by id.
   *
   * @param id Batch id
   * @return the found {@link Client} or {@link NoResultException} if no one was found.
   */
  public Client findById(UUID id) {
    requireIdNonNull(id);
    return dao.findById(id);
  }

  /**
   * Create or update a {@link Client}.
   *
   * @param entity {@link Client} to be saved
   * @return {@link Client}
   */
  @Transactional
  public Client save(Client entity) {
    Objects.requireNonNull(entity, "entity cannot be null");
    return dao.save(entity);
  }

  /**
   * Delete a {@link Client}.
   *
   * @param id {@link UUID} of client to be deleted
   */
  @Transactional
  public void delete(UUID id) {
    requireIdNonNull(id);
    Client entity = dao.findById(id);
    dao.delete(entity);
  }

  protected void requireIdNonNull(Object id) {
    Objects.requireNonNull(id, "id of entity cannot be null");
  }
}
