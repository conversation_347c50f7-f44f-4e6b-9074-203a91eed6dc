package com.tr.bluemoon.accountingentryintegration.domain.synchronization;

import com.google.common.base.Strings;
import com.tr.bluemoon.accountingentryintegration.domain.integration.Client;
import com.tr.bluemoon.accountingentryintegration.domain.integration.ClientNotEnabledException;
import com.tr.bluemoon.accountingentryintegration.domain.synchronization.dto.ClientV3Dto;
import com.tr.bluemoon.bracct.commons.exception.BusinessException;
import com.tr.bluemoon.bracct.commons.util.UuidUtils;
import com.tr.bluemoon.core.CoreServicesException;
import com.tr.bluemoon.core.NationalIdentity;
import java.time.Clock;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;
import javax.inject.Inject;
import javax.persistence.NoResultException;

/**
 * Service class to handle {@link Client} synchronization business logic.
 *
 * <AUTHOR> <PERSON>scimento
 */
public class ClientSynchronizationService {

  public static final String CNPJ = "BR-CNPJ";
  public static final String CPF = "BR-CPF";
  public static final String CEI = "BR-CEI";
  public static final String CAEPF = "BR-CAEPF";
  private static final String CLIENT_NOT_EXISTS = "Client does not exist";

  private final ClientCoreService clientCoreService;
  private final ClientService clientService;

  @Inject
  public ClientSynchronizationService(
      ClientService clientService, ClientCoreService clientCoreService) {
    this.clientCoreService = clientCoreService;
    this.clientService = clientService;
  }

  /**
   * find or synchronize a {@link Client} by client id.
   *
   * @param clientId client id
   * @return {@link Client}
   * @throws BusinessException if the client does not exist
   */
  public Client findOrSynchronizeClientId(String clientId) {
    try {
      return clientService.findById(clientId);
    } catch (NoResultException e) {
      try {
        Client client = new Client();
        client.setId(UuidUtils.fromString(clientId));
        return synchronizeByClientId(client);
      } catch (BusinessException e1) {
        throw new ClientNotEnabledException();
      }
    }
  }

  /**
   * Create or update a {@link Client} by client id with data from Core.
   *
   * @param client with id to create or update
   * @return {@link Client}
   * @throws BusinessException if the client does not exist
   */
  public Client synchronizeByClientId(Client client) {
    try {
      ClientV3Dto dto = clientCoreService.get(UuidUtils.fromUUID(client.getId()));
      return synchronizeByDto(dto, client);
    } catch (NoResultException | CoreServicesException e) {
      throw new BusinessException(CLIENT_NOT_EXISTS);
    }
  }

  protected Client synchronizeByDto(ClientV3Dto dto, Client entity) {
    try {
      Objects.requireNonNull(dto);

      convertToEntity(entity, dto);

      entity = clientService.save(entity);

      return entity;
    } catch (NullPointerException e) {
      throw new BusinessException(CLIENT_NOT_EXISTS);
    }
  }

  protected void convertToEntity(Client entity, ClientV3Dto dto) {
    entity.setId(UuidUtils.fromString(dto.getId()));
    entity.setCode(dto.getCode());
    entity.setName(dto.getName());
    entity.setNationalIdentity(null);
    entity.setNationalIdentityType(null);
    entity.setSynchronizeDate(LocalDateTime.now(Clock.systemUTC()));

    if (dto.getPrimaryContactExpanded() != null) {
      if (dto.getPrimaryContactExpanded().getNationalIdentitiesExpanded() != null) {
        Optional<NationalIdentity> optionaNationalIdentity =
            dto.getPrimaryContactExpanded().getNationalIdentitiesExpanded().stream()
                .filter(this::isBrIdentityInscription)
                .findFirst();
        if (optionaNationalIdentity.isPresent()) {
          entity.setNationalIdentity(
              removeNonNumericCharacters(optionaNationalIdentity.get().getMasked()));
          entity.setNationalIdentityType(optionaNationalIdentity.get().getKind().getId());
        }
      }
    }
  }

  protected String removeNonNumericCharacters(String maskedIdentity) {
    return maskedIdentity.replaceAll("[^\\d]", "");
  }

  protected boolean isBrIdentityInscription(NationalIdentity identity) {
    return !Strings.isNullOrEmpty(identity.getMasked())
        && (identity.getKind().getId().equals(CNPJ)
            || identity.getKind().getId().equals(CPF)
            || identity.getKind().getId().equals(CEI)
            || identity.getKind().getId().equals(CAEPF));
  }
}
