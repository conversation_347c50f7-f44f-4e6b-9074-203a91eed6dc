package com.tr.bluemoon.accountingentryintegration.domain.synchronization.dto;

import com.tr.bluemoon.core.BaseCoreServicesDto;
import com.tr.bluemoon.core.ClientMain;
import com.tr.bluemoon.core.ContactV4;
import com.tr.bluemoon.core.NamedReference;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ClientV3Dto extends BaseCoreServicesDto {

  private String companyId;
  private Integer netClientId;
  private String code;
  private String name;
  private NamedReference nameFormat;
  private NamedReference status;
  private NamedReference primaryContact;
  private ContactV4 primaryContactExpanded;
  private ClientMain clientMainExpanded;
}
