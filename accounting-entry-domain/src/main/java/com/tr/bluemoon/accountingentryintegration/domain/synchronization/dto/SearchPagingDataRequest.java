package com.tr.bluemoon.accountingentryintegration.domain.synchronization.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SearchPagingDataRequest {

  public static final Integer DEFAULT_ITEMS_PER_PAGE = 25;
  public static final Integer DEFAULT_PAGE_INDEX = 1;

  private Integer pageIndex;
  private Integer itemsPerPage = DEFAULT_ITEMS_PER_PAGE;

  public Integer getStartIndex() {
    return null;
  }

  public Integer getPageIndex() {
    return pageIndex == null ? DEFAULT_PAGE_INDEX : pageIndex;
  }

  public Integer getItemsPerPage() {
    return itemsPerPage == null ? DEFAULT_ITEMS_PER_PAGE : itemsPerPage;
  }

  public boolean hasPagination() {
    return pageIndex != null;
  }
}
