package com.tr.bluemoon.accountingentryintegration.domain.xml;

import com.tr.bluemoon.accountingentryintegration.domain.file.InvalidStructureException;
import java.io.IOException;
import java.io.InputStream;
import javax.xml.transform.Source;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.Validator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.xml.sax.SAXException;

@Service
@RequiredArgsConstructor
public class XmlAccountingEntryValidatorService {

  private final Schema schema;

  public void validateXml(InputStream xml) {
    try {
      Source xmlFile = new StreamSource(xml);
      Validator validator = schema.newValidator();
      validator.validate(xmlFile);
    } catch (SAXException | IOException exception) {
      throw new InvalidStructureException(exception.getMessage());
    }
  }
}
