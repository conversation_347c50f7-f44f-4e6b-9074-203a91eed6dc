<?xml version="1.0" encoding="UTF-8"?>
<persistence version="2.1"
	xmlns="http://xmlns.jcp.org/xml/ns/persistence" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/persistence http://xmlns.jcp.org/xml/ns/persistence/persistence_2_1.xsd">

	<persistence-unit name="BR_ACCOUNTING_ENTRY_INTEGRATION_COMPANY_PU"
		transaction-type="RESOURCE_LOCAL">
		<provider>org.eclipse.persistence.jpa.PersistenceProvider</provider>

		<class>com.tr.bluemoon.accountingentryintegration.domain.file.File</class>
		<class>com.tr.bluemoon.accountingentryintegration.domain.document.Document</class>
		<class>com.tr.bluemoon.accountingentryintegration.domain.rehydration.Rehydration</class>
		<class>com.tr.bluemoon.accountingentryintegration.domain.rehydration.RehydrationDocument</class>

		<exclude-unlisted-classes>true</exclude-unlisted-classes>
		<shared-cache-mode>NONE</shared-cache-mode>

		<properties>
			<property name="eclipselink.session.customizer"
				value="com.tr.bluemoon.bracct.commons.jpa.UUIDGenerator" />
			<property name="eclipselink.jdbc.cache-statements" value="true" />
			<property name="javax.persistence.validation.mode" value="none"/>
			
<!-- 			<property name="eclipselink.logging.level" value="INFO" /> -->
<!-- 			<property name="eclipselink.logging.level.sql" value="FINE" /> -->
<!-- 			<property name="eclipselink.logging.parameters" value="true" /> -->
			<property name="eclipselink.jdbc.allow-native-sql-queries" value="true" />

			<!-- required to lazy loading. See performJPAWeaving gradle task -->
			<property name="eclipselink.weaving" value="static" />
			<property name="eclipselink.weaving.changetracking" value="false" />
		</properties>
	</persistence-unit>

	<persistence-unit name="BR_ACCOUNTING_ENTRY_INTEGRATION_PU" transaction-type="RESOURCE_LOCAL">
		<provider>org.eclipse.persistence.jpa.PersistenceProvider</provider>

		<exclude-unlisted-classes>false</exclude-unlisted-classes>
		<shared-cache-mode>NONE</shared-cache-mode>

		<properties>
			<property name="eclipselink.session.customizer"
				value="com.tr.bluemoon.bracct.commons.jpa.UUIDGenerator" />
			<property name="eclipselink.jdbc.cache-statements" value="true" />
<!-- 			<property name="eclipselink.logging.level" value="INFO" /> -->
<!-- 			<property name="eclipselink.logging.level.sql" value="FINE" /> -->
<!-- 			<property name="eclipselink.logging.parameters" value="true" /> -->
		</properties>
	</persistence-unit>

	<persistence-unit name="BR_ACCT_INVOICE_INTEGRATION_COMPANY_PU" transaction-type="RESOURCE_LOCAL">
		<provider>org.eclipse.persistence.jpa.PersistenceProvider</provider>

		<class>com.tr.bluemoon.accountingentryintegration.domain.integration.ClientApiIntegration</class>
		<class>com.tr.bluemoon.accountingentryintegration.domain.integration.Client</class>

		<exclude-unlisted-classes>true</exclude-unlisted-classes>
		<shared-cache-mode>NONE</shared-cache-mode>

		<properties>
			<property name="eclipselink.session.customizer" value="com.tr.bluemoon.bracct.commons.jpa.UUIDGenerator" />
			<property name="eclipselink.jdbc.cache-statements" value="true" />
			<property name="javax.persistence.validation.mode" value="none"/>

			<!-- 			<property name="eclipselink.logging.level" value="INFO" /> -->
			<!-- 			<property name="eclipselink.logging.level.sql" value="FINE" /> -->
			<!-- 			<property name="eclipselink.logging.parameters" value="true" /> -->
			<property name="eclipselink.jdbc.allow-native-sql-queries" value="true" />

			<!-- required to lazy loading. See performJPAWeaving gradle task -->
			<property name="eclipselink.weaving" value="static" />
			<property name="eclipselink.weaving.changetracking" value="false" />
		</properties>
	</persistence-unit>

	<persistence-unit name="BR_ACCT_INVOICE_INTEGRATION_PU" transaction-type="RESOURCE_LOCAL">
		<provider>org.eclipse.persistence.jpa.PersistenceProvider</provider>

		<exclude-unlisted-classes>false</exclude-unlisted-classes>
		<shared-cache-mode>NONE</shared-cache-mode>

		<properties>
			<property name="eclipselink.session.customizer"
				value="com.tr.bluemoon.bracct.commons.jpa.UUIDGenerator" />
			<property name="eclipselink.jdbc.cache-statements" value="true" />
			<!-- 			<property name="eclipselink.logging.level" value="INFO" /> -->
			<!-- 			<property name="eclipselink.logging.level.sql" value="FINE" /> -->
			<!-- 			<property name="eclipselink.logging.parameters" value="true" /> -->
		</properties>
	</persistence-unit>

</persistence>
