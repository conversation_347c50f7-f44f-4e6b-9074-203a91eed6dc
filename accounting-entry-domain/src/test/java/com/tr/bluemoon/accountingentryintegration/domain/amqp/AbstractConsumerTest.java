package com.tr.bluemoon.accountingentryintegration.domain.amqp;

import io.cloudevents.CloudEvent;
import io.cloudevents.core.builder.CloudEventBuilder;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.amqp.AmqpRejectAndDontRequeueException;
import org.springframework.amqp.ImmediateAcknowledgeAmqpException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

class AbstractConsumerTest {

  @Test
  void shouldMoveToDeadLetterWhenMaxRetriesAchieved() {
    final var consumer = Mockito.spy(AbstractConsumer.class);
    final var event = cloudEvent("event", "rk");
    final Map<Object, Integer> death = new HashMap<>();
    final var rabbitTemplate = Mockito.mock(RabbitTemplate.class);

    Mockito.doReturn(3).when(consumer).maxRetries("event");
    death.put("count", 3);

    consumer.rabbitTemplate = rabbitTemplate;

    final var ex =
        Assertions.assertThrows(
            ImmediateAcknowledgeAmqpException.class,
            () -> consumer.handleRetry(event, death, "EXCHANGE", new NullPointerException()));
    Assertions.assertEquals("Max retries reached.", ex.getMessage());

    Mockito.verify(rabbitTemplate)
        .convertAndSend(Mockito.eq("EXCHANGE"), Mockito.eq("rk"), Mockito.any(CloudEvent.class));
  }

  @Test
  void shouldNackRequeueFalseWhenAttemptsNotExceeded() {
    final var consumer = Mockito.spy(AbstractConsumer.class);
    final var event = cloudEvent("event", "rk");
    final Map<?, ?> death = new HashMap<>();
    final var rabbitTemplate = Mockito.mock(RabbitTemplate.class);

    Mockito.doReturn(3).when(consumer).maxRetries("event");

    consumer.rabbitTemplate = rabbitTemplate;

    final var ex =
        Assertions.assertThrows(
            AmqpRejectAndDontRequeueException.class,
            () -> consumer.handleRetry(event, death, "EXCHANGE", new NullPointerException()));
    Assertions.assertEquals("Attempt 1. Retrying...", ex.getMessage());
  }

  private CloudEvent cloudEvent(String event, String rk) {
    return CloudEventBuilder.v1()
        .withId(UUID.randomUUID().toString())
        .withTime(OffsetDateTime.now())
        .withSource(URI.create("onvio.com.br"))
        .withType(rk)
        .withSubject(UUID.randomUUID().toString())
        .withDataContentType("application/json")
        .withData(event.getBytes(StandardCharsets.UTF_8))
        .build();
  }
}
