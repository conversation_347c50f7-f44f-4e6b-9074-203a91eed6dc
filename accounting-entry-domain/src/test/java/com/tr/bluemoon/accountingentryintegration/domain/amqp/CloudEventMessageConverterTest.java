package com.tr.bluemoon.accountingentryintegration.domain.amqp;

import io.cloudevents.core.v1.CloudEventBuilder;
import java.net.URI;
import java.net.URISyntaxException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.support.converter.MessageConversionException;

class CloudEventMessageConverterTest {

  @Test
  void shouldThrowMessageConversionExceptionWhenConvertToMessage() {
    // Arrange
    CloudEventMessageConverter converter = new CloudEventMessageConverter();
    var message = "any message";
    var properties = new MessageProperties();

    // Act
    Assertions.assertThrows(
        MessageConversionException.class, () -> converter.toMessage(message, properties));
  }

  @Test
  void shouldConvertToMessage() throws URISyntaxException {
    // Arrange
    CloudEventMessageConverter converter = new CloudEventMessageConverter();
    var event =
        new CloudEventBuilder().withId("eventId").withSource(new URI("")).withType("type").build();
    var properties = new MessageProperties();

    // Act
    var message = converter.toMessage(event, properties);

    // Assert
    Assertions.assertNotNull(message);
  }
}
