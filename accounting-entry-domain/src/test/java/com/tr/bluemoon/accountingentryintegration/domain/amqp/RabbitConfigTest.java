package com.tr.bluemoon.accountingentryintegration.domain.amqp;

import com.tr.bluemoon.commons.cryptography.security.EncryptionException;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

class RabbitConfigTest {

  @Test
  void shouldDoRabbitDeclarations() {
    final var config = Mockito.spy(new RabbitConfig());
    final var rabbitAdmin = Mockito.mock(RabbitAdmin.class);
    final var rabbitTemplate = Mockito.mock(RabbitTemplate.class);
    final var exchanges =
        List.of(buildExchangeDefinition("EXCHANGE"), buildExchangeDefinition("EXCHANGE2"));
    final var queues = List.of(buildQueueDefinition());
    final var bindings =
        List.of(buildBindingDefinitionToExchange(), buildBindingDefinitionToQueue());

    Mockito.doReturn(rabbitAdmin).when(config).createRabbitAdmin(Mockito.any(RabbitTemplate.class));

    config.amqpAdmin(rabbitTemplate, exchanges, queues, bindings);

    Mockito.verify(rabbitAdmin, Mockito.times(1))
        .declareExchange(
            Mockito.argThat(
                exchange ->
                    "EXCHANGE".equals(exchange.getName())
                        && "topic".equals(exchange.getType())
                        && exchange.isDurable()
                        && exchange.isAutoDelete()
                        && !exchange.isInternal()
                        && exchange.getArguments().containsKey("x-args")
                        && exchange.getArguments().get("x-args").equals("10")));

    Mockito.verify(rabbitAdmin, Mockito.times(1))
        .declareExchange(
            Mockito.argThat(
                exchange ->
                    "EXCHANGE2".equals(exchange.getName())
                        && "topic".equals(exchange.getType())
                        && exchange.isDurable()
                        && exchange.isAutoDelete()
                        && !exchange.isInternal()
                        && exchange.getArguments().containsKey("x-args")
                        && exchange.getArguments().get("x-args").equals("10")));

    Mockito.verify(rabbitAdmin, Mockito.times(1))
        .declareQueue(
            Mockito.argThat(
                queue ->
                    "QUEUE".equals(queue.getName())
                        && queue.isDurable()
                        && !queue.isAutoDelete()
                        && !queue.isExclusive()
                        && queue.getArguments().containsKey("x-args")
                        && queue.getArguments().get("x-args").equals("12")));

    Mockito.verify(rabbitAdmin, Mockito.times(1))
        .declareBinding(
            Mockito.argThat(
                binding ->
                    "EXCHANGE2".equals(binding.getDestination())
                        && !binding.isDestinationQueue()
                        && "EXCHANGE".equals(binding.getExchange())
                        && "RK".equals(binding.getRoutingKey())));

    Mockito.verify(rabbitAdmin, Mockito.times(1))
        .declareBinding(
            Mockito.argThat(
                binding ->
                    "QUEUE".equals(binding.getDestination())
                        && binding.isDestinationQueue()
                        && "EXCHANGE".equals(binding.getExchange())
                        && "RK".equals(binding.getRoutingKey())));
  }

  @Test
  void shouldDumbTestConstructor() throws EncryptionException {
    Assertions.assertEquals("", new ExchangeDefinition().getName());
    Assertions.assertEquals("", new QueueDefinition().getName());
    Assertions.assertNull(new BindingDefinition().getExchange());
    Assertions.assertEquals("x", new BindingDefinition("x", true, "dest", "rk").getExchange());
    Assertions.assertEquals("x", new RabbitMQServer().getExchange("x").getName());
  }

  private ExchangeDefinition buildExchangeDefinition(String name) {
    final var exchangeDefinition = new ExchangeDefinition(name, true, true);
    exchangeDefinition.addArgument("x-args", "10");
    exchangeDefinition.setInternal(false);

    return exchangeDefinition;
  }

  private QueueDefinition buildQueueDefinition() {
    final var queueDefinition = new QueueDefinition("QUEUE", true);
    queueDefinition.addArgument("x-args", "12");

    return queueDefinition;
  }

  private BindingDefinition buildBindingDefinitionToExchange() {
    final var from = buildExchangeDefinition("EXCHANGE");
    final var to = buildExchangeDefinition("EXCHANGE2");
    return new BindingDefinition(from, to, "RK");
  }

  private BindingDefinition buildBindingDefinitionToQueue() {
    final var from = buildExchangeDefinition("EXCHANGE");
    final var to = buildQueueDefinition();
    return new BindingDefinition(from, to, "RK");
  }
}
