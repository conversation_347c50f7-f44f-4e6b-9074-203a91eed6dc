package com.tr.bluemoon.accountingentryintegration.domain.amqp;

import com.rabbitmq.client.AMQP;
import com.tr.bluemoon.bracct.commons.UserInfo;
import com.tr.bluemoon.bracct.rabbitmq.Exchange;
import com.tr.bluemoon.bracct.rabbitmq.MessageProducer;
import com.tr.bluemoon.brtap.commons.security.ContextHolder;
import com.tr.bluemoon.brtap.commons.security.SessionAndBindings;
import com.tr.bluemoon.brtap.commons.security.UdsSession;
import com.tr.bluemoon.brtap.commons.security.UdsSessionBindings;
import com.tr.bluemoon.commons.configuration.BlueMoonDomain;
import com.tr.bluemoon.commons.configuration.IACList;
import io.cloudevents.CloudEvent;
import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.UUID;
import java.util.concurrent.TimeoutException;
import javax.ws.rs.InternalServerErrorException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

class RabbitMQEnqueuerTest {

  private static final UUID chainId = UUID.fromString("abd34287-1715-43ff-8fc7-cad4831637a9");
  private static final UUID companyId = UUID.fromString("eb5e6ef1-2e2a-409f-9873-ed8c8ce63b90");
  private static final UUID contactId = UUID.fromString("08ebe468-9af5-4f95-b157-838e914612dd");
  private static final UUID userId = UUID.fromString("f668f5b2-df13-47cc-b62d-3f35a9d7124b");
  private static final UUID sessionId = UUID.fromString("2e5b7581-31b0-454d-a714-70956b23a53e");
  private static final String EXCHANGE = "exchange";
  private static final String ROUTING_KEY = "rk";

  @Mock private RabbitMQServer server;

  @Mock private UserInfo userInfo;

  @Mock private MessageProducer producer;

  @BeforeEach
  void setup() {
    MockitoAnnotations.openMocks(this);

    BlueMoonDomain.setDomain("ci.onvio.com.br", "https");

    Mockito.when(userInfo.getCompanyId()).thenReturn(companyId);
    Mockito.when(userInfo.getContactId()).thenReturn(contactId);
    Mockito.when(userInfo.getAuthToken()).thenReturn("UDSLongToken ABC");

    final var exchange = Mockito.mock(Exchange.class);
    Mockito.when(server.getExchange(EXCHANGE)).thenReturn(exchange);
    Mockito.when(exchange.getMessageProducer(false)).thenReturn(producer);
  }

  @Test
  void shouldPublishMessageWithRabbitServer() throws IOException, TimeoutException {
    final var enqueuer = new RabbitMQEnqueuer(server);
    final var event = new Event();

    enqueuer.publishMessage(event, ROUTING_KEY, EXCHANGE, userInfo);

    final ArgumentCaptor<byte[]> bytesCaptor = ArgumentCaptor.forClass(byte[].class);
    final ArgumentCaptor<AMQP.BasicProperties> propertiesCaptor =
        ArgumentCaptor.forClass(AMQP.BasicProperties.class);
    Mockito.verify(producer)
        .produce(
            bytesCaptor.capture(),
            Mockito.eq(ROUTING_KEY),
            propertiesCaptor.capture(),
            Mockito.anyMap());

    final var cloudEvent = CloudEventMessageConverter.convertFromBytes(bytesCaptor.getValue());
    Assertions.assertEquals("ci.onvio.com.br", cloudEvent.getSource().toString());
    Assertions.assertEquals(
        "eb5e6ef1-2e2a-409f-9873-ed8c8ce63b90", cloudEvent.getExtension("comtrbluemooncompanyid"));
    Assertions.assertEquals(
        "08ebe468-9af5-4f95-b157-838e914612dd", cloudEvent.getExtension("comtrbluemooncontactid"));
    Assertions.assertEquals("rk", cloudEvent.getType());
    Assertions.assertEquals("8a362b33-afb6-4b7f-8f70-398ca41a8f86", cloudEvent.getSubject());
    Assertions.assertEquals("application/json", cloudEvent.getDataContentType());
  }

  @Test
  void shouldPublishMessageWithRabbitTemplate() throws IOException {
    // Arrange
    final var rabbitTemplate = Mockito.mock(RabbitTemplate.class);
    final var enqueuer = new RabbitMQEnqueuer(server);
    final var event = new Event();
    final var context = new ContextHolder();
    final var iacList = new IACList(Collections.emptySet());
    final var sessionAndBindings = new SessionAndBindings();
    final var applicationConfiguration = new HashMap<String, String>();
    final var udsSessionBindings = new UdsSessionBindings();
    final var udsSession = new UdsSession();

    enqueuer.rabbitTemplate = rabbitTemplate;

    sessionAndBindings.setUdsSession(udsSession);
    udsSessionBindings.setApplicationConfiguration(applicationConfiguration);
    sessionAndBindings.setUdsSessionBindings(udsSessionBindings);

    applicationConfiguration.put("CompanyId", companyId.toString());
    applicationConfiguration.put("ContactId", contactId.toString());

    context.setBlueMoonDomain(
        com.tr.bluemoon.brtap.commons.domainawareness.BlueMoonDomain.of(
            "ci.onvio.com.br", "https", iacList));
    context.setSessionAndBindings(sessionAndBindings);

    // Action
    enqueuer.publishMessage(event, ROUTING_KEY, EXCHANGE, context);

    // Assertions
    final ArgumentCaptor<CloudEvent> eventCaptor = ArgumentCaptor.forClass(CloudEvent.class);
    Mockito.verify(rabbitTemplate)
        .convertAndSend(Mockito.eq(EXCHANGE), Mockito.eq(ROUTING_KEY), eventCaptor.capture());

    final var cloudEvent = eventCaptor.getValue();
    Assertions.assertEquals("ci.onvio.com.br", cloudEvent.getSource().toString());
    Assertions.assertEquals(
        "eb5e6ef1-2e2a-409f-9873-ed8c8ce63b90", cloudEvent.getExtension("comtrbluemooncompanyid"));
    Assertions.assertEquals(
        "08ebe468-9af5-4f95-b157-838e914612dd", cloudEvent.getExtension("comtrbluemooncontactid"));
    Assertions.assertEquals("rk", cloudEvent.getType());
    Assertions.assertEquals("8a362b33-afb6-4b7f-8f70-398ca41a8f86", cloudEvent.getSubject());
    Assertions.assertEquals("application/json", cloudEvent.getDataContentType());
  }

  @Test
  void shouldThrowInternalServerErrorExceptionWhenPublishMessageWithRabbitServer()
      throws IOException, TimeoutException {
    final var enqueuer = new RabbitMQEnqueuer(server);
    final var event = new Event();

    final ArgumentCaptor<byte[]> bytesCaptor = ArgumentCaptor.forClass(byte[].class);
    final ArgumentCaptor<AMQP.BasicProperties> propertiesCaptor =
        ArgumentCaptor.forClass(AMQP.BasicProperties.class);
    Mockito.doThrow(IOException.class)
        .when(producer)
        .produce(
            bytesCaptor.capture(),
            Mockito.eq(ROUTING_KEY),
            propertiesCaptor.capture(),
            Mockito.anyMap());

    Assertions.assertThrows(
        InternalServerErrorException.class,
        () -> enqueuer.publishMessage(event, ROUTING_KEY, EXCHANGE, userInfo));
  }

  private static class Event implements RabbitMQEvent {

    @Override
    public UUID getId() {
      return UUID.fromString("8a362b33-afb6-4b7f-8f70-398ca41a8f86");
    }
  }
}
