package com.tr.bluemoon.accountingentryintegration.domain.amqp;

import static com.tr.bluemoon.accountingentryintegration.domain.amqp.RoutingPathsConfig.BR_ACCOUNTING_ENTRY_INTEGRATION_SERVICES;

import com.tr.bluemoon.brtap.commons.domainawareness.RoutingPaths;
import io.cloudevents.CloudEvent;
import io.cloudevents.core.builder.CloudEventBuilder;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.OffsetDateTime;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.core.MessageProperties;

public class RabbitPostProcessorTests {

  public static final UUID contactId = UUID.fromString("08ebe468-9af5-4f95-b157-838e914612dd");

  private static final UUID companyId = UUID.fromString("eb5e6ef1-2e2a-409f-9873-ed8c8ce63b90");
  private static final UUID chainId = UUID.fromString("abd34287-1715-43ff-8fc7-cad4831637a9");
  private static final UUID userId = UUID.fromString("f668f5b2-df13-47cc-b62d-3f35a9d7124b");
  private static final UUID sessionId = UUID.fromString("2e5b7581-31b0-454d-a714-70956b23a53e");

  private static final String TEST_SERVICES = "TEST";
  private static final RoutingPaths routingPaths =
      new RoutingPaths()
          .put(TEST_SERVICES, true, "/api/test")
          .put(BR_ACCOUNTING_ENTRY_INTEGRATION_SERVICES, false, "/api/br-accounting-entry");

  @Test
  public void shouldPopulateContextFromMessage() {
    final CloudEvent event =
        CloudEventBuilder.v1()
            .withId(UUID.randomUUID().toString())
            .withTime(OffsetDateTime.now())
            .withSource(URI.create("onvio.com.br"))
            .withExtension("comtrbluemoonchainid", chainId.toString())
            .withExtension("comtrbluemooncompanyid", companyId.toString())
            .withExtension("comtrbluemooncontactid", contactId.toString())
            .withExtension("comtrbluemoonuserid", userId.toString())
            .withExtension("comtrbluemoonsessionid", sessionId.toString())
            .withType("rk")
            .withSubject(UUID.randomUUID().toString())
            .withDataContentType("application/json")
            .withData("{}".getBytes(StandardCharsets.UTF_8))
            .build();

    CloudEventMessageConverter converter = new CloudEventMessageConverter();
    RabbitPostProcessor postProcessor = new RabbitPostProcessor(converter, routingPaths);

    postProcessor.postProcessMessage(converter.toMessage(event, new MessageProperties()));

    Assertions.assertEquals(
        "https://int.onvio.com.br/api/test",
        RabbitPostProcessor.getContext().getRoutingTable().get(TEST_SERVICES));
    Assertions.assertEquals(companyId, RabbitPostProcessor.getContext().getCompanyId());
    Assertions.assertEquals(contactId, RabbitPostProcessor.getContext().getContactId());
    Assertions.assertEquals(userId, RabbitPostProcessor.getContext().getUserId());
    Assertions.assertEquals(
        "onvio.com.br", RabbitPostProcessor.getContext().getBlueMoonDomain().getDomain());
  }
}
