package com.tr.bluemoon.accountingentryintegration.domain.aws;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.ws.rs.InternalServerErrorException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import software.amazon.awssdk.awscore.exception.AwsServiceException;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.core.sync.ResponseTransformer;
import software.amazon.awssdk.http.AbortableInputStream;
import software.amazon.awssdk.http.SdkHttpRequest;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.CopyObjectRequest;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;

class S3ServiceTest {

  private static final String S3_BUCKET_KEY = "br.s3.bucket";

  @Test
  void shouldSaveXml() {
    // Arrange
    final var companyId = UUID.fromString("a667c802-fda4-4a0b-82ea-f0979fba931c");
    final var clientId = UUID.fromString("a288d9f5-3bc2-468d-a42f-f3e05e940e8d");
    final var fileId = UUID.fromString("33e1fbb4-c4ed-47ce-8f9d-3dede7937eed");
    final var s3Client = Mockito.mock(S3Client.class);
    final var service = Mockito.spy(new S3Service());
    service.s3Client = s3Client;

    System.setProperty(S3_BUCKET_KEY, "bucketName");

    Mockito.doReturn(fileId).when(service).randomUuid();

    try (InputStream xml =
        new ByteArrayInputStream("<xml></xml>".getBytes(StandardCharsets.UTF_8))) {
      // Action
      final var key = service.saveXml(xml, "folder", clientId, companyId, "test.onvio.com.br");

      // Assertions
      Assertions.assertEquals(
          "test.onvio.com.br/a667c802-fda4-4a0b-82ea-f0979fba931c"
              + "/api/accountingentries/folder/a288d9f5-3bc2-468d-a42f-f3e05e940e8d/33e1fbb4-c4ed-47ce-8f9d-3dede7937eed.xml",
          key);

      Mockito.verify(s3Client)
          .putObject(
              Mockito.<PutObjectRequest>argThat(
                  request ->
                      "bucketName".equals(request.bucket())
                          && key.equals(request.key())
                          && "DaysToExpire=60".equals(request.tagging())),
              Mockito.any(RequestBody.class));
    } catch (IOException e) {
      Assertions.fail("It should be able to create a ByteArrayInputStream");
    }
  }

  @Test
  void shouldSaveXmlThrowInternalServerErrorIfFailed() {
    // Arrange
    final var companyId = UUID.fromString("a667c802-fda4-4a0b-82ea-f0979fba931c");
    final var clientId = UUID.fromString("a288d9f5-3bc2-468d-a42f-f3e05e940e8d");
    final var fileId = UUID.fromString("33e1fbb4-c4ed-47ce-8f9d-3dede7937eed");
    final var s3Client = Mockito.mock(S3Client.class);
    final var service = Mockito.spy(new S3Service());
    service.s3Client = s3Client;

    System.setProperty(S3_BUCKET_KEY, "bucketName");

    Mockito.doReturn(fileId).when(service).randomUuid();
    Mockito.when(
            s3Client.putObject(Mockito.any(PutObjectRequest.class), Mockito.any(RequestBody.class)))
        .thenThrow(AwsServiceException.create("Error!", null));

    try (InputStream xml =
        new ByteArrayInputStream("<xml></xml>".getBytes(StandardCharsets.UTF_8))) {
      Assertions.assertThrows(
          InternalServerErrorException.class,
          () -> service.saveXml(xml, "folder", clientId, companyId, "test.onvio.com.br"));
    } catch (IOException e) {
      Assertions.fail("It should be able to create a ByteArrayInputStream");
    }
  }

  @Test
  @SuppressWarnings("unchecked")
  void shouldGetXmlAsInputStream() throws IOException {
    // Arrange
    System.setProperty(S3_BUCKET_KEY, "bucketName");

    final var s3Client = Mockito.mock(S3Client.class);
    final var key = "key";
    final var service = new S3Service();
    service.s3Client = s3Client;

    try (InputStream is =
        new ByteArrayInputStream("<xml></xml>".getBytes(StandardCharsets.UTF_8))) {
      final ResponseInputStream<GetObjectResponse> response =
          new ResponseInputStream<>(
              GetObjectResponse.builder().build(), AbortableInputStream.create(is, () -> {}));

      Mockito.when(
              s3Client.getObject(
                  Mockito.any(GetObjectRequest.class), Mockito.any(ResponseTransformer.class)))
          .thenReturn(response);
      Mockito.when(s3Client.getObject(Mockito.any(GetObjectRequest.class))).thenCallRealMethod();

      // Action
      InputStream result = service.getXmlContentInputStream(key);

      // Assertions
      Assertions.assertEquals(
          "<xml></xml>", new String(result.readAllBytes(), StandardCharsets.UTF_8));
      Mockito.verify(s3Client)
          .getObject(
              Mockito.<GetObjectRequest>argThat(
                  request -> "bucketName".equals(request.bucket()) && "key".equals(request.key())));
    }
  }

  @Test
  void shouldPreSignUrl() {
    // Arrange
    final var s3Presigner = Mockito.mock(S3Presigner.class);
    final var httpRequest = Mockito.mock(SdkHttpRequest.class);
    final var service = new S3Service();
    service.s3Presigner = s3Presigner;

    System.setProperty(S3_BUCKET_KEY, "bucket");

    Mockito.when(httpRequest.getUri()).thenReturn(URI.create("https://s3/presigned-url"));
    final var getObjectRequest =
        PresignedGetObjectRequest.builder()
            .httpRequest(httpRequest)
            .expiration(Instant.now())
            .isBrowserExecutable(true)
            .signedHeaders(Map.of("x", List.of("y")))
            .build();

    Mockito.when(s3Presigner.presignGetObject(Mockito.any(GetObjectPresignRequest.class)))
        .thenReturn(getObjectRequest);

    // Action
    service.getPreSignedDownloadUrl("key", Duration.ofHours(1));

    // Assertions
    Mockito.verify(s3Presigner)
        .presignGetObject(
            Mockito.<GetObjectPresignRequest>argThat(
                request ->
                    request.signatureDuration().equals(Duration.ofHours(1))
                        && request.getObjectRequest().bucket().equals("bucket")
                        && request.getObjectRequest().key().equals("key")));
  }

  @Test
  void shouldDeleteXml() {
    // Arrange
    final var service = new S3Service();
    final var s3Client = Mockito.mock(S3Client.class);
    service.s3Client = s3Client;

    System.setProperty(S3_BUCKET_KEY, "bucket");

    // Action
    service.deleteXml("key");

    // Assertions
    Mockito.verify(s3Client)
        .deleteObject(
            Mockito.<DeleteObjectRequest>argThat(
                request -> request.bucket().equals("bucket") && request.key().equals("key")));
  }

  @Test
  void shouldCopyXml() {
    // Arrange
    final var companyId = UUID.fromString("a667c802-fda4-4a0b-82ea-f0979fba931c");
    final var clientId = UUID.fromString("a288d9f5-3bc2-468d-a42f-f3e05e940e8d");
    final var fileId = UUID.fromString("33e1fbb4-c4ed-47ce-8f9d-3dede7937eed");
    final var s3Client = Mockito.mock(S3Client.class);
    final var service = Mockito.spy(new S3Service());
    service.s3Client = s3Client;

    System.setProperty(S3_BUCKET_KEY, "bucketName");

    Mockito.doReturn(fileId).when(service).randomUuid();

    // Action
    final var key = service.copyXml("origin", "folder", clientId, companyId, "test.onvio.com.br");

    // Assertions
    Assertions.assertEquals(
        "test.onvio.com.br/a667c802-fda4-4a0b-82ea-f0979fba931c"
            + "/api/accountingentries/folder/a288d9f5-3bc2-468d-a42f-f3e05e940e8d/33e1fbb4-c4ed-47ce-8f9d-3dede7937eed.xml",
        key);

    Mockito.verify(s3Client).copyObject(Mockito.any(CopyObjectRequest.class));
  }
}
