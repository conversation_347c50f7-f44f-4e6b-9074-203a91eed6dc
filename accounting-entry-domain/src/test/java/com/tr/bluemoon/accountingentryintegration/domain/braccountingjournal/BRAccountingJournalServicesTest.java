package com.tr.bluemoon.accountingentryintegration.domain.braccountingjournal;

import com.tr.bluemoon.bracct.commons.UserInfo;
import java.math.BigDecimal;
import java.net.URI;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import javax.ws.rs.core.Response;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

class BRAccountingJournalServicesTest {

  private static final String AUTH_TOKEN = "UDSLongToken 495E49CC0E7742A9946C1192239802F5";
  private static final UUID PROTOCOL_ID = UUID.fromString("d3f11cbf-2366-4bed-a0b3-e0c6bb369104");

  @Mock
  private UserInfo userInfo;

  @Mock
  private Response response;

  @Spy
  @InjectMocks
  private BRAccountingJournalServices service;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    Mockito.when(userInfo.getAuthToken()).thenReturn(AUTH_TOKEN);
  }

  @Test
  void shouldGetThirdPartyIntegration() {
    // Arrange
    Mockito.doReturn("https://test.onvio.com.br/api/br-accounting-journal").when(service).getBRAccountingJournalServicesServicesRoute();
    Mockito.doReturn(response)
        .when(service)
        .getJournalResponse(Mockito.any(URI.class));

    Mockito.when(response.getStatus()).thenReturn(200);
    Mockito.when(response.readEntity(JournalResponseDto.class)).thenReturn(buildJournalResponseDto());

    // Act
    service.getThirdPartyIntegration(PROTOCOL_ID);

    Mockito.verify(service)
        .getJournalResponse(Mockito.argThat(uri ->
            uri.toString().equals("https://test.onvio.com.br/api/br-accounting-journal/v1/third-party-integration/d3f11cbf-2366-4bed-a0b3-e0c6bb369104")));
  }

  @Test
  void shouldGetThirdPartyIntegrationWhenStatusDiffer200() {
    // Arrange
    Mockito.doReturn("https://test.onvio.com.br/api/br-accounting-journal").when(service).getBRAccountingJournalServicesServicesRoute();
    Mockito.doReturn(response)
        .when(service)
        .getJournalResponse(Mockito.any(URI.class));

    Mockito.when(response.getStatus()).thenReturn(500);
    Mockito.when(response.readEntity(JournalResponseDto.class)).thenReturn(buildJournalResponseDto());

    // Act
    service.getThirdPartyIntegration(PROTOCOL_ID);

    Mockito.verify(service)
        .getJournalResponse(Mockito.argThat(uri ->
            uri.toString().equals("https://test.onvio.com.br/api/br-accounting-journal/v1/third-party-integration/d3f11cbf-2366-4bed-a0b3-e0c6bb369104")));
  }

  private JournalResponseDto buildJournalResponseDto() {
    final JournalResponseDto journalResponseDto = new JournalResponseDto();
    journalResponseDto.setJournals(List.of(buildJournalDto()));
    return journalResponseDto;
  }

  private JournalDto buildJournalDto() {
    final JournalDto journalDto = new JournalDto();
    journalDto.setType("string");
    journalDto.setDate(LocalDate.now());
    journalDto.setStatus("Sucesso");
    journalDto.setStatusMessage("Concluído com Sucesso");
    journalDto.setJournalEntries(List.of(buildJournalEntryDto()));
    return journalDto;
  }

  private JournalEntryDto buildJournalEntryDto() {
    final JournalEntryDto journalDto = new JournalEntryDto();
    journalDto.setAccountCode(NumberUtils.INTEGER_ONE);
    journalDto.setType("string");
    journalDto.setValue(BigDecimal.ONE);
    journalDto.setHistoryCode(NumberUtils.INTEGER_TWO);
    journalDto.setHistory("history");
    return journalDto;
  }

}
