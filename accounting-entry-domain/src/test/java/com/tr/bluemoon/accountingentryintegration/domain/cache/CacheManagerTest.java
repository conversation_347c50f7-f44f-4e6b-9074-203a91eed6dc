package com.tr.bluemoon.accountingentryintegration.domain.cache;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

import com.fasterxml.jackson.core.type.TypeReference;
import com.tr.bluemoon.accountingentryintegration.domain.integration.Client;
import io.lettuce.core.SetArgs;
import io.lettuce.core.api.sync.RedisCommands;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * {@link CacheManager} test.
 *
 * <AUTHOR> Nascimento
 */
class CacheManagerTest {

  @InjectMocks private CacheManager service;

  @BeforeEach
  void setUp() throws Exception {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  void shouldGetFromCache() {
    // Arrange
    CacheManager service = Mockito.spy(this.service);

    UUID id = UUID.randomUUID();

    String key = "key";
    String object = "{\"id\": \"" + id + "\"}";

    @SuppressWarnings("unchecked")
    RedisCommands<String, String> command = Mockito.mock(RedisCommands.class);

    Mockito.doReturn(command).when(service).sync();
    Mockito.doReturn(object).when(command).get(key);

    // Act
    Client client = service.get(key, new TypeReference<Client>() {});

    // Assert
    assertEquals(id, client.getId());
  }

  @Test
  void shouldGetAListFromCache() {
    // Arrange
    CacheManager service = Mockito.spy(this.service);

    UUID id1 = UUID.randomUUID();
    UUID id2 = UUID.randomUUID();

    String key = "key";
    String object = "[{\"id\": \"" + id1 + "\"}, {\"id\": \"" + id2 + "\"}]";

    @SuppressWarnings("unchecked")
    RedisCommands<String, String> command = Mockito.mock(RedisCommands.class);

    Mockito.doReturn(command).when(service).sync();
    Mockito.doReturn(object).when(command).get(key);

    // Act
    List<Client> clients = service.get(key, new TypeReference<>() {});

    // Assert
    assertEquals(2, clients.size());
    assertFalse(clients.isEmpty());
    assertEquals(id1, clients.get(0).getId());
    assertEquals(id2, clients.get(1).getId());
  }

  @Test
  void shouldGetWithLoad() {
    // Arrange
    CacheManager service = Mockito.spy(this.service);

    UUID id = UUID.randomUUID();

    String key = "key";
    String object = "{\"id\": \"" + id + "\"}";

    @SuppressWarnings("unchecked")
    RedisCommands<String, String> command = Mockito.mock(RedisCommands.class);

    Mockito.doReturn(command).when(service).sync();
    Mockito.doReturn(object).when(command).get(key);

    // Act
    Client client = service.get(key, new TypeReference<>() {}, Client::new);

    // Assert
    assertEquals(id, client.getId());
  }

  @Test
  void shouldGetAListWithLoad() {
    // Arrange
    CacheManager service = Mockito.spy(this.service);

    UUID id1 = UUID.randomUUID();
    UUID id2 = UUID.randomUUID();

    String key = "key";
    String object = "[{\"id\": \"" + id1 + "\"}, {\"id\": \"" + id2 + "\"}]";

    @SuppressWarnings("unchecked")
    RedisCommands<String, String> command = Mockito.mock(RedisCommands.class);

    Mockito.doReturn(command).when(service).sync();
    Mockito.doReturn(object).when(command).get(key);

    // Act
    List<Client> clients = service.get(key, new TypeReference<List<Client>>() {}, ArrayList::new);

    // Assert
    assertEquals(2, clients.size());
    assertFalse(clients.isEmpty());
    assertEquals(id1, clients.get(0).getId());
    assertEquals(id2, clients.get(1).getId());
  }

  @Test
  void shouldLoadWhenNotFound() {
    // Arrange
    CacheManager service = Mockito.spy(this.service);

    UUID id = UUID.randomUUID();

    final String key = "key";
    final String object = null;

    final Client client = new Client();
    client.setId(id);

    @SuppressWarnings("unchecked")
    RedisCommands<String, String> command = Mockito.mock(RedisCommands.class);

    Mockito.doReturn(command).when(service).sync();
    Mockito.doNothing().when(service).set(key, client);
    Mockito.doReturn(object).when(command).get(key);

    // Act
    Client result = service.get(key, new TypeReference<Client>() {}, () -> client);

    // Assert
    assertEquals(id, result.getId());
    Mockito.verify(service).set(key, client);
  }

  @Test
  void shouldLoadWhenNotFoundAndNotSetToCache() {
    // Arrange
    CacheManager service = Mockito.spy(this.service);

    UUID id = UUID.randomUUID();

    final String key = "key";
    final String object = null;

    final Client client = new Client();
    client.setId(id);

    @SuppressWarnings("unchecked")
    RedisCommands<String, String> command = Mockito.mock(RedisCommands.class);

    Mockito.doReturn(command).when(service).sync();
    Mockito.doNothing().when(service).set(key, client);
    Mockito.doReturn(object).when(command).get(key);

    // Act
    Client result = service.get(key, new TypeReference<Client>() {}, () -> client, false);

    // Assert
    assertEquals(id, result.getId());
    Mockito.verify(service, Mockito.never()).set(key, client);
  }

  @Test
  void shouldLoadAndSetWhenActualCacheIsInvalid() {
    // Arrange
    CacheManager service = Mockito.spy(this.service);

    UUID id = UUID.randomUUID();

    final String key = "key";
    final String object = "aaa";

    final Client client = new Client();
    client.setId(id);

    @SuppressWarnings("unchecked")
    RedisCommands<String, String> command = Mockito.mock(RedisCommands.class);

    Mockito.doReturn(command).when(service).sync();
    Mockito.doNothing().when(service).set(key, client);
    Mockito.doReturn(object).when(command).get(key);

    // Act
    Client result = service.get(key, new TypeReference<Client>() {}, () -> client);

    // Assert
    assertEquals(id, result.getId());
    Mockito.verify(service).set(key, client);
  }

  @Test
  void shouldLoadAndDoNotSetWhenActualCacheIsInvalid() {
    // Arrange
    CacheManager service = Mockito.spy(this.service);

    UUID id = UUID.randomUUID();

    final String key = "key";
    final String object = "aaa";

    final Client client = new Client();
    client.setId(id);

    @SuppressWarnings("unchecked")
    RedisCommands<String, String> command = Mockito.mock(RedisCommands.class);

    Mockito.doReturn(command).when(service).sync();
    Mockito.doNothing().when(service).set(key, client);
    Mockito.doReturn(object).when(command).get(key);

    // Act
    Client result = service.get(key, new TypeReference<Client>() {}, () -> client, false);

    // Assert
    assertEquals(id, result.getId());
    Mockito.verify(service, Mockito.never()).set(key, client);
  }

  @Test
  void shouldClearCacheByKey() {
    // Arrange
    CacheManager service = Mockito.spy(this.service);

    String key = "key";

    @SuppressWarnings("unchecked")
    RedisCommands<String, String> command = Mockito.mock(RedisCommands.class);

    Mockito.doReturn(command).when(service).sync();
    Mockito.doReturn(1L).when(command).del(key);

    // Act
    service.evict(key);

    // Assert
    Mockito.verify(command).del(key);
  }

  @Test
  void shouldSetCatch() {
    // Arrange
    CacheManager service = Mockito.spy(this.service);

    String key = "key";
    Client client = new Client();
    client.setId(UUID.randomUUID());

    @SuppressWarnings("unchecked")
    RedisCommands<String, String> command = Mockito.mock(RedisCommands.class);

    Mockito.doReturn(command).when(service).sync();
    Mockito.doReturn("")
        .when(command)
        .set(Mockito.eq(key), Mockito.anyString(), Mockito.any(SetArgs.class));

    // Act
    service.set(key, client);

    // Assert
    Mockito.verify(command).set(Mockito.eq(key), Mockito.anyString(), Mockito.any(SetArgs.class));
  }
}
