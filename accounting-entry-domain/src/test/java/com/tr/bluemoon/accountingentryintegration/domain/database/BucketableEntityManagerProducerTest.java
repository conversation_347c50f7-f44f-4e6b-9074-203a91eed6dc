package com.tr.bluemoon.accountingentryintegration.domain.database;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mockStatic;

import com.tr.bluemoon.accountingentryintegration.database.BucketedPersistenceManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

class BucketableEntityManagerProducerTest {


  @Mock
  private BucketedPersistenceManager bpmMock;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void isDatabasesUpReturnsTrueWhenQuerySucceeds() {
    try (MockedStatic<BucketedPersistenceManager> mockedStatic = mockStatic(BucketedPersistenceManager.class)) {
      mockedStatic.when(() -> BucketedPersistenceManager.getInstance(any(), any())).thenReturn(bpmMock);

      doNothing().when(bpmMock).runOnAllBuckets(any());
      boolean result = BucketableEntityManagerProducer.isDatabasesUp();

      assertTrue(result);
    }
  }


  @Test
  void isDatabasesUpReturnsFalseWhenQueryFails() {
    try (MockedStatic<BucketedPersistenceManager> mockedStatic = mockStatic(BucketedPersistenceManager.class)) {
      mockedStatic.when(() -> BucketedPersistenceManager.getInstance(any(), any())).thenReturn(bpmMock);

      doThrow(new RuntimeException()).when(bpmMock).runOnAllBuckets(any());
      boolean result = BucketableEntityManagerProducer.isDatabasesUp();

      assertFalse(result);
    }
  }

}