package com.tr.bluemoon.accountingentryintegration.domain.database;

import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitPostProcessor;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitPostProcessorTests;
import com.tr.bluemoon.bracct.commons.jpa.model.CompanyOwnershipEntity;
import com.tr.bluemoon.brtap.commons.security.ContextHolder;
import java.time.Clock;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.Year;
import java.time.ZoneId;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class CompatibleAuditableListenerTest {

  private Clock defaultFixedClock;

  @BeforeEach
  void setUp() {
    // fixed clock at 2017-01-09 10:10:30
    final var instant =
        Year.of(2017)
            .atMonth(Month.JANUARY)
            .atDay(9)
            .atTime(10, 10, 30)
            .atZone(ZoneId.systemDefault())
            .toInstant();
    this.defaultFixedClock = Clock.fixed(instant, ZoneId.systemDefault());
  }

  @Test
  void shouldDelegateToSuperAuditableWhenCDI() {
    RabbitPostProcessor.cleanContext();
    final var listener = Mockito.spy(new CompatibleAuditableListener());
    final var entity = new DumbEntity();

    Mockito.doNothing().when(listener).invokeParentPrePersist(entity);
    Mockito.doNothing().when(listener).invokeParentPreUpdate(entity);

    listener.onPrePersist(entity);
    listener.onPreUpdate(entity);

    Mockito.verify(listener).invokeParentPrePersist(entity);
    Mockito.verify(listener).invokeParentPreUpdate(entity);
  }

  @Test
  void shouldCallbackPrePersistFromContext() {
    final var entity = new DumbEntity();
    new RabbitPostProcessorTests().shouldPopulateContextFromMessage();

    final var listener = new CompatibleAuditableListener();
    listener.timer = defaultFixedClock;

    listener.onPrePersist(entity);

    Assertions.assertEquals(RabbitPostProcessorTests.contactId, entity.getCreatedBy());
    Assertions.assertEquals(LocalDateTime.now(defaultFixedClock), entity.getCreated());
  }

  @Test
  void shouldCallbackPreUpdateFromContext() {
    final var contextHolder = Mockito.mock(ContextHolder.class);
    final var entity = new DumbEntity();
    new RabbitPostProcessorTests().shouldPopulateContextFromMessage();

    final var listener = new CompatibleAuditableListener();
    listener.timer = defaultFixedClock;

    listener.onPreUpdate(entity);

    Assertions.assertEquals(RabbitPostProcessorTests.contactId, entity.getChangedBy());
    Assertions.assertEquals(LocalDateTime.now(defaultFixedClock), entity.getChanged());
  }

  @Getter
  @Setter
  private static class DumbEntity extends CompanyOwnershipEntity<UUID> {
    private UUID id;
  }
}
