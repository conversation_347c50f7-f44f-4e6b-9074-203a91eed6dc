package com.tr.bluemoon.accountingentryintegration.domain.document;

import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitConfig;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitMQEnqueuer;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitMQEvent;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitMQListEvent;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitPostProcessor;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitPostProcessorTests;
import com.tr.bluemoon.accountingentryintegration.domain.aws.S3Service;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class DocumentDeliveryProducerTest {

  private static final UUID document1Id = UUID.randomUUID();
  private static final UUID fileId = UUID.randomUUID();
  private static final String PATH_TO_FILE = "pathToFile";
  private static final Duration PRE_SIGNED_SHORT_DURATION = Duration.ofHours(1);
  private static final UUID clientId = UUID.randomUUID();

  @Mock private RabbitMQEnqueuer enqueuer;

  @Mock private S3Service s3Service;

  @InjectMocks private DocumentDeliveryProducer producer;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    Mockito.when(s3Service.getPreSignedDownloadUrl(Mockito.anyString(), Mockito.any()))
        .thenReturn("pre-signed");

    new RabbitPostProcessorTests().shouldPopulateContextFromMessage();
  }

  @Test
  void shouldSendDocumentToSocket() {
    // Arrange
    final var document = new Document();
    document.setId(document1Id);
    document.setCreated(LocalDateTime.of(2022, 5, 10, 10, 20));
    document.setFileId(fileId);
    document.setFilePath(PATH_TO_FILE);
    document.setClientId(clientId);

    // Action
    producer.sendToSocket(document);

    // Assertions
    Mockito.verify(s3Service)
        .getPreSignedDownloadUrl(
            Mockito.argThat(PATH_TO_FILE::equals), Mockito.eq(PRE_SIGNED_SHORT_DURATION));
    Mockito.verify(enqueuer)
        .publishMessage(
            Mockito.argThat(event -> assertPayloadEvent(event, false, false)),
            Mockito.eq(RabbitConfig.DOCUMENT_AVAILABLE_EVENT),
            Mockito.eq(RabbitConfig.ACCOUNTING_ENTRY_INTEGRATION_EXCHANGE),
            Mockito.eq(RabbitPostProcessor.getContext()));
  }

  @Test
  void shouldSendDocumentsAsPackageToSocket() {
    // Arrange
    final var documents = documents(1);

    // Action
    producer.sendToSocket(documents, true);

    // Assertions
    Mockito.verify(s3Service)
        .getPreSignedDownloadUrl(
            Mockito.argThat(PATH_TO_FILE::equals), Mockito.eq(PRE_SIGNED_SHORT_DURATION));
    Mockito.verify(enqueuer)
        .publishMessage(
            Mockito.argThat(event -> assertPayloadEvent(event, true, false)),
            Mockito.eq(RabbitConfig.DOCUMENT_AVAILABLE_EVENT),
            Mockito.eq(RabbitConfig.ACCOUNTING_ENTRY_INTEGRATION_EXCHANGE),
            Mockito.eq(RabbitPostProcessor.getContext()));
  }

  @Test
  void shouldSendDocumentsAsPackageToSocketHavingMore() {
    // Arrange
    final var documents = documents(1001);

    // Action
    producer.sendToSocket(documents, true);

    // Assertions
    Mockito.verify(s3Service, Mockito.times(1000))
        .getPreSignedDownloadUrl(
            Mockito.argThat(PATH_TO_FILE::equals), Mockito.eq(PRE_SIGNED_SHORT_DURATION));
    Mockito.verify(enqueuer)
        .publishMessage(
            Mockito.argThat(event -> assertPayloadEvent(event, true, true)),
            Mockito.eq(RabbitConfig.DOCUMENT_AVAILABLE_EVENT),
            Mockito.eq(RabbitConfig.ACCOUNTING_ENTRY_INTEGRATION_EXCHANGE),
            Mockito.eq(RabbitPostProcessor.getContext()));
  }

  private List<Document> documents(int number) {
    final var list = new ArrayList<Document>();

    for (int i = 0; i < number; i++) {
      final var doc = new Document();
      doc.setId(document1Id);
      doc.setCreated(LocalDateTime.of(2022, 5, 10, 10, 20));
      doc.setFileId(fileId);
      doc.setFilePath(PATH_TO_FILE);
      doc.setClientId(clientId);

      list.add(doc);
    }
    return list;
  }

  @SuppressWarnings("unchecked")
  private boolean assertPayloadEvent(RabbitMQEvent event, boolean isPackage, boolean hasMore) {
    if (!(event instanceof RabbitMQListEvent)) {
      return false;
    }

    final RabbitMQListEvent<DocumentAvailableEvent> events =
        (RabbitMQListEvent<DocumentAvailableEvent>) event;
    final var documentEvent = events.getItems().get(0);

    return isPackage == events.isPackage()
        && hasMore == events.isHasMore()
        && documentEvent.getId().equals(fileId)
        && documentEvent.getCreated().equals(LocalDateTime.of(2022, 5, 10, 10, 20))
        && documentEvent.getShortUrl().equals("pre-signed")
        && documentEvent
            .getLocation()
            .equals(
                "https://onvio.com.br/api/br-accounting-entry/v1/documents/"
                    + document1Id
                    + "/download")
        && documentEvent.getClientId().equals(clientId);
  }
}
