package com.tr.bluemoon.accountingentryintegration.domain.document;

import static com.tr.bluemoon.accountingentryintegration.domain.amqp.RoutingPathsConfig.BR_ACCOUNTING_ENTRY_INTEGRATION_SERVICES;
import static com.tr.bluemoon.accountingentryintegration.domain.document.DocumentEventMapper.toEvent;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.tr.bluemoon.accountingentryintegration.domain.amqp.CloudEventMessageConverter;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitPostProcessor;
import com.tr.bluemoon.brtap.commons.domainawareness.RoutingPaths;
import io.cloudevents.core.builder.CloudEventBuilder;
import java.net.URI;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.core.MessageProperties;

class DocumentEventMapperTest {

  private static final RoutingPaths routingPaths =
      new RoutingPaths()
          .put(BR_ACCOUNTING_ENTRY_INTEGRATION_SERVICES, false, "/api/br-accounting-entry");

  @BeforeEach
  public void loadContext() {
    CloudEventMessageConverter converter = new CloudEventMessageConverter();
    RabbitPostProcessor postProcessor = new RabbitPostProcessor(converter, routingPaths);

    postProcessor.postProcessMessage(converter.toMessage(CloudEventBuilder.v1()
        .withId(UUID.randomUUID().toString())
        .withSource(URI.create("onvio.com.br"))
        .withType("rk")
        .build(), new MessageProperties()));
  }

  @Test
  public void shouldConverEntityToEvent() {
    Document documentEntity = new Document();
    documentEntity.setId(UUID.randomUUID());
    documentEntity.setFileId(UUID.randomUUID());
    documentEntity.setClientId(UUID.randomUUID());
    documentEntity.setFilePath("path");

    DocumentEvent documentEvent = toEvent(documentEntity);

    assertEquals(documentEntity.getId(), documentEvent.getId());
    assertEquals(documentEntity.getFileId(), documentEvent.getFileId());
    assertEquals(documentEntity.getClientId(), documentEvent.getClientId());
    assertEquals("path", documentEvent.getPath());
  }
}