package com.tr.bluemoon.accountingentryintegration.domain.document;

import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitPostProcessorTests;
import com.tr.bluemoon.accountingentryintegration.domain.aws.S3Service;
import java.io.IOException;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class DocumentServiceTest {

  private static final String DOCUMENT_FOLDER = "document";

  @Mock DocumentRepository repository;

  @Mock S3Service s3Service;

  @InjectMocks DocumentService service;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void shouldSaveDocument() {
    // Arrange
    final var document = new Document();

    // Act
    service.save(document);

    // Assert
    Mockito.verify(repository).save(document);
  }

  @Test
  void shouldThrowNullPointerExceptionWhenSaveNullDocument() {
    // Act
    Assertions.assertThrows(NullPointerException.class, () -> service.save(null));

    // Assert
    Mockito.verify(repository, Mockito.never()).save(Mockito.any(Document.class));
  }

  @Test
  void shouldCreateDocument() throws IOException {
    // Arrange
    final var fileId = UUID.randomUUID();
    final var clientId = UUID.randomUUID();
    final var companyId = UUID.randomUUID();
    final String filePath = "filePath";

    // Prepare context
    new RabbitPostProcessorTests().shouldPopulateContextFromMessage();

    // Act
    service.createDocument(fileId, clientId, companyId, filePath);

    // Assert
    Mockito.verify(repository).save(Mockito.any(Document.class));
    Mockito.verify(s3Service)
        .copyXml(filePath, DOCUMENT_FOLDER, clientId, companyId, "onvio.com.br");
  }

  @Test
  void shouldInvokeRepositoryWhenFindById() {
    // Arrange
    final var id = UUID.randomUUID();

    // Act
    service.findById(id);

    // Assert
    Mockito.verify(repository).findById(id);
  }

  @Test
  void shouldThrowNullPointerExceptionWhenFindByNullId() {
    // Act
    Assertions.assertThrows(NullPointerException.class, () -> service.findById(null));

    // Assert
    Mockito.verify(repository, Mockito.never()).findById(Mockito.any(UUID.class));
  }

  @Test
  void shouldUpdateDocumentDownloadDetails() {
    // Arrange
    Document document = new Document();
    document.setDownloadAttempts(0);
    document.setDownloaded(false);

    // Act
    service.documentDownloaded(document);

    // Assert
    Assertions.assertTrue(document.isDownloaded());
    Assertions.assertEquals(1, document.getDownloadAttempts());
    Assertions.assertNotNull(document.getLastDownloadDate());
    Mockito.verify(repository).save(document);
  }

  @Test
  void shouldThrowNullPointerExceptionWhenDocumentDownloadedWithNullDocument() {
    // Act & Assert
    Assertions.assertThrows(NullPointerException.class, () -> service.documentDownloaded(null));
    Mockito.verify(repository, Mockito.never()).save(Mockito.any(Document.class));
  }
}
