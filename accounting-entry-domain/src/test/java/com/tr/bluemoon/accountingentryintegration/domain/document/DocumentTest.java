package com.tr.bluemoon.accountingentryintegration.domain.document;

import java.time.LocalDateTime;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class DocumentTest {

  @Test
  void shouldUpdatePersistentEntityLastStatusOnWhenUpdateStatus() {
    LocalDateTime lastStatusOn = LocalDateTime.of(2022, 1, 1, 12, 0, 0);

    // Arrange
    final var document = new Document();
    document.setId(UUID.randomUUID());
    document.setLastStatusOn(lastStatusOn);

    // Act
    document.setStatus(DocumentStatus.SENT);

    // Assert
    Assertions.assertNotEquals(lastStatusOn, document.getLastStatusOn());
    Assertions.assertEquals(DocumentStatus.SENT, document.getStatus());
  }

  @Test
  void shouldNotUpdateLastStatusOnWithSameStatus() {
    LocalDateTime lastStatusOn = LocalDateTime.of(2022, 1, 1, 12, 0, 0);

    // Arrange
    final var document = new Document();
    document.setId(UUID.randomUUID());
    document.setLastStatusOn(lastStatusOn);

    // Act
    document.setStatus(DocumentStatus.AWAITING_TO_SEND);

    // Assert
    Assertions.assertEquals(lastStatusOn, document.getLastStatusOn());
    Assertions.assertEquals(DocumentStatus.AWAITING_TO_SEND, document.getStatus());
  }

  @Test
  void lastStatusOnShouldNotBeNull() {
    // Arrange
    final var document = new Document();

    // Assert
    Assertions.assertNotNull(document.getLastStatusOn());
  }
}
