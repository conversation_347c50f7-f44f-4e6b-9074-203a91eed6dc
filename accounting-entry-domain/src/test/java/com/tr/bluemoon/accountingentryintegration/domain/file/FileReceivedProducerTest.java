package com.tr.bluemoon.accountingentryintegration.domain.file;

import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitConfig;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitMQEnqueuer;
import com.tr.bluemoon.bracct.commons.UserInfo;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class FileReceivedProducerTest {

  @Mock RabbitMQEnqueuer enqueuer;

  @Mock UserInfo userInfo;

  @InjectMocks FileReceivedProducer producer;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void shouldProduceFileReceivedMessage() {
    final var event = createEvent();

    // Act
    producer.sendToValidate(event);

    // Assert
    Mockito.verify(enqueuer)
        .publishMessage(
            Mockito.eq(event),
            Mockito.eq(FileReceivedConfig.FILE_RECEIVED_EVENT),
            Mockito.eq(RabbitConfig.ACCOUNTING_ENTRY_INTEGRATION_EXCHANGE),
            Mockito.eq(userInfo));
  }

  private FileReceivedEvent createEvent() {
    var event = new FileReceivedEvent();
    event.setId(UUID.randomUUID());
    return event;
  }
}
