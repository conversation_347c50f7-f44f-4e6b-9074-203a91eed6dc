package com.tr.bluemoon.accountingentryintegration.domain.file;

import static org.mockito.ArgumentMatchers.any;

import com.tr.bluemoon.accountingentryintegration.domain.aws.S3Service;
import com.tr.bluemoon.accountingentryintegration.domain.document.Document;
import com.tr.bluemoon.accountingentryintegration.domain.document.DocumentDeliveryProducer;
import com.tr.bluemoon.accountingentryintegration.domain.document.DocumentProducer;
import com.tr.bluemoon.accountingentryintegration.domain.document.DocumentService;
import com.tr.bluemoon.accountingentryintegration.domain.xml.XmlAccountingEntryValidatorService;
import com.tr.bluemoon.bracct.commons.exception.BusinessException;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;
import javax.ws.rs.InternalServerErrorException;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class FileReceivedServiceTest {

  @Mock private FileService fileService;

  @Mock private DocumentService documentService;

  @Mock private S3Service s3Service;

  @Mock private DocumentDeliveryProducer documentDeliveryProducer;

  @Mock private DocumentProducer documentProducer;

  @Mock private XmlAccountingEntryValidatorService xmlAccountingEntryValidatorService;

  @InjectMocks private FileReceivedService service;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void shouldValidateFileAndSaveStatusValidated() throws IOException {
    // Arrange
    final var file = createFile();
    final var fileId = file.getId();
    final var clientId = file.getClientId();
    final var companyId = file.getCompanyId();
    InputStream content = IOUtils.toInputStream("file", "UTF-8");

    Mockito.doReturn(file).when(fileService).findById(fileId);

    Mockito.when(s3Service.getXmlContentInputStream("filePath")).thenReturn(content);
    Mockito.when(documentService.createDocument(fileId, clientId, companyId, file.getFilePath()))
        .thenReturn(new Document());

    // Act
    service.validate(fileId);

    // Assert
    Assertions.assertEquals(FileStatus.VALIDATED, file.getStatus());
    Assertions.assertEquals(1, file.getTotalValidationAttempts());

    Mockito.verify(documentService, Mockito.times(1))
        .createDocument(fileId, clientId, companyId, file.getFilePath());
    Mockito.verify(fileService, Mockito.times(1)).save(file);
    Mockito.verify(xmlAccountingEntryValidatorService, Mockito.times(1)).validateXml(content);
    Mockito.verify(documentDeliveryProducer, Mockito.times(1))
        .sendToSocket(any(Document.class));
  }

  @Test
  void shouldNotValidateFileAlreadyValidated() throws IOException {
    // Arrange
    final var file = createFile();
    file.setStatus(FileStatus.VALIDATED);
    final var fileId = file.getId();

    Mockito.doReturn(file).when(fileService).findById(fileId);

    // Act
    service.validate(fileId);

    // Assert
    Mockito.verify(s3Service, Mockito.never()).getXmlContentInputStream(Mockito.anyString());
    Mockito.verify(documentService, Mockito.never())
        .createDocument(
            any(UUID.class),
            any(UUID.class),
            any(UUID.class),
            Mockito.anyString());
    Mockito.verify(fileService, Mockito.never()).save(any(File.class));
  }

  @Test
  void shouldValidateFileAndSaveStatusNotFound() throws IOException {
    // Arrange
    final var file = createFile();
    final var fileId = file.getId();

    Mockito.doReturn(file).when(fileService).findById(fileId);

    Mockito.when(s3Service.getXmlContentInputStream("filePath")).thenReturn(null);

    // Act
    service.validate(fileId);

    // Assert
    Assertions.assertEquals(FileStatus.FILE_NOT_FOUND, file.getStatus());
    Assertions.assertEquals(1, file.getTotalValidationAttempts());

    Mockito.verify(documentService, Mockito.never())
        .createDocument(
            any(UUID.class),
            any(UUID.class),
            any(UUID.class),
            Mockito.anyString());
    Mockito.verify(fileService, Mockito.times(1)).save(file);
  }

  @Test
  void shouldThrowInternalServerErrorExceptionWhenFileHasMinimumValidationAttempts()
      throws IOException {
    // Arrange
    final var file = createFile();
    final var fileId = file.getId();

    Mockito.doReturn(file).when(fileService).findById(fileId);

    Mockito.doThrow(new BusinessException()).when(s3Service).getXmlContentInputStream("filePath");

    // Act
    Assertions.assertThrows(InternalServerErrorException.class, () -> service.validate(fileId));
  }

  @Test
  void shouldValidateWhenFileHasMaximumValidationAttemptsAndSaveStatusGenericError()
      throws IOException {
    // Arrange
    final var file = createFile();
    final var fileId = file.getId();

    file.setTotalValidationAttempts(2);

    Mockito.doReturn(file).when(fileService).findById(fileId);

    Mockito.doThrow(new BusinessException()).when(s3Service).getXmlContentInputStream("filePath");

    // Act
    Assertions.assertThrows(InternalServerErrorException.class, () -> service.validate(fileId));

    // Assert
    Assertions.assertEquals(FileStatus.GENERIC_ERROR, file.getStatus());
    Assertions.assertEquals(3, file.getTotalValidationAttempts());

    Mockito.verify(documentService, Mockito.never())
        .createDocument(
            any(UUID.class),
            any(UUID.class),
            any(UUID.class),
            Mockito.anyString());
    Mockito.verify(fileService, Mockito.times(1)).save(file);
  }

  @Test
  void shouldValidateWhenFileIsInvalidAndSaveStatusInvalidStructure() throws IOException {
    // Arrange
    final var file = createFile();
    final var fileId = file.getId();
    final var clientId = file.getClientId();
    final var companyId = file.getCompanyId();
    InputStream content = IOUtils.toInputStream("file", "UTF-8");

    Mockito.doReturn(file).when(fileService).findById(fileId);

    Mockito.when(s3Service.getXmlContentInputStream("filePath")).thenReturn(content);
    Mockito.doNothing().when(s3Service).deleteXml(file.getFilePath());
    Mockito.doThrow(new InvalidStructureException("invalid"))
        .when(xmlAccountingEntryValidatorService)
        .validateXml(content);

    // Act
    service.validate(fileId);

    // Assert
    Assertions.assertEquals(FileStatus.INVALID_STRUCTURE, file.getStatus());
    Assertions.assertEquals(1, file.getTotalValidationAttempts());
    Assertions.assertNull(file.getFilePath());

    Mockito.verify(documentService, Mockito.never())
        .createDocument(
            any(UUID.class),
            any(UUID.class),
            any(UUID.class),
            Mockito.anyString());
    Mockito.verify(fileService, Mockito.times(1)).save(file);
    Mockito.verify(s3Service, Mockito.times(1)).deleteXml("filePath");
    Mockito.verify(xmlAccountingEntryValidatorService, Mockito.times(1)).validateXml(content);
    Mockito.verify(documentDeliveryProducer, Mockito.never())
        .sendToSocket(any(Document.class));
  }

  @Test
  void shouldValidateFileAndSendToOnvio() throws IOException {
    // Arrange
    final var file = createFile();
    file.setOnvio(Boolean.TRUE);

    final var fileId = file.getId();
    final var clientId = file.getClientId();
    final var companyId = file.getCompanyId();
    InputStream content = IOUtils.toInputStream("file", "UTF-8");

    Mockito.doReturn(file).when(fileService).findById(fileId);

    Mockito.when(s3Service.getXmlContentInputStream("filePath")).thenReturn(content);
    Mockito.when(documentService.createDocument(fileId, clientId, companyId, file.getFilePath()))
        .thenReturn(new Document());

    // Act
    service.validate(fileId);

    // Assert
    Assertions.assertEquals(FileStatus.VALIDATED, file.getStatus());
    Assertions.assertEquals(1, file.getTotalValidationAttempts());

    Mockito.verify(documentService, Mockito.times(1))
        .createDocument(fileId, clientId, companyId, file.getFilePath());
    Mockito.verify(fileService, Mockito.times(1)).save(file);
    Mockito.verify(xmlAccountingEntryValidatorService, Mockito.times(1)).validateXml(content);
    Mockito.verify(documentDeliveryProducer, Mockito.never())
        .sendToSocket(any(Document.class));
    Mockito.verify(documentProducer, Mockito.times(1)).publishDocument(any(Document.class));
  }

  @Test
  void shouldValidateFileAndSendToContabil() throws IOException {
    // Arrange
    final var file = createFile();
    file.setOnvio(Boolean.FALSE);

    final var fileId = file.getId();
    final var clientId = file.getClientId();
    final var companyId = file.getCompanyId();
    InputStream content = IOUtils.toInputStream("file", "UTF-8");

    Mockito.doReturn(file).when(fileService).findById(fileId);

    Mockito.when(s3Service.getXmlContentInputStream("filePath")).thenReturn(content);
    Mockito.when(documentService.createDocument(fileId, clientId, companyId, file.getFilePath()))
        .thenReturn(new Document());

    // Act
    service.validate(fileId);

    // Assert
    Assertions.assertEquals(FileStatus.VALIDATED, file.getStatus());
    Assertions.assertEquals(1, file.getTotalValidationAttempts());

    Mockito.verify(documentService, Mockito.times(1))
        .createDocument(fileId, clientId, companyId, file.getFilePath());
    Mockito.verify(fileService, Mockito.times(1)).save(file);
    Mockito.verify(xmlAccountingEntryValidatorService, Mockito.times(1)).validateXml(content);
    Mockito.verify(documentDeliveryProducer, Mockito.times(1))
        .sendToSocket(any(Document.class));
    Mockito.verify(documentProducer, Mockito.never()).publishDocument(any(Document.class));
  }

  private File createFile() {
    final var file = new File();
    file.setId(UUID.randomUUID());
    file.setClientId(UUID.randomUUID());
    file._persistence_set_companyId(UUID.randomUUID());
    file.setFilePath("filePath");
    return file;
  }
}
