package com.tr.bluemoon.accountingentryintegration.domain.file;

import com.tr.bluemoon.accountingentryintegration.domain.aws.S3Service;
import com.tr.bluemoon.bracct.commons.UserInfo;
import com.tr.bluemoon.commons.configuration.BlueMoonDomain;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;
import javax.persistence.NoResultException;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class FileServiceTest {

  private static final String FILE_FOLDER = "file";

  @Mock private FileRepository repository;

  @Mock private S3Service s3Service;

  @Mock private UserInfo userInfo;

  @InjectMocks private FileService service;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void shouldRequireIdWhenFindByIdAndClientId() {
    final String id = null;
    final UUID clientId = UUID.randomUUID();

    NullPointerException ex =
        Assertions.assertThrows(
            NullPointerException.class, () -> service.findByIdAndClientId(id, clientId));

    Assertions.assertEquals("id cannot be null", ex.getMessage());
  }

  @Test
  void shouldRequireClientIdWhenFindByIdAndClientId() {
    final String id = UUID.randomUUID().toString();
    final UUID clientId = null;

    NullPointerException ex =
        Assertions.assertThrows(
            NullPointerException.class, () -> service.findByIdAndClientId(id, clientId));

    Assertions.assertEquals("clientId cannot be null", ex.getMessage());
  }

  @Test
  void shouldThrowInvalidProtocolWhenFindByIdAndClientIdWithInvalidProtocolFormat() {
    final String id = "sahd";
    final UUID clientId = UUID.randomUUID();

    InvalidProtocolException ex =
        Assertions.assertThrows(
            InvalidProtocolException.class, () -> service.findByIdAndClientId(id, clientId));

    Assertions.assertEquals(404, ex.getError().getCode());
    Assertions.assertEquals("Invalid Protocol Format", ex.getError().getMessage());
  }

  @Test
  void shouldThrowProtocolNotFoundWhenFindByIdAndClientIdOfAnInexistentFile() {
    final String id = UUID.randomUUID().toString();
    final UUID clientId = UUID.randomUUID();

    Mockito.when(service.findByIdAndClientId(id, clientId))
        .thenThrow(new NoResultException("no result"));

    ProtocolNotFoundException ex =
        Assertions.assertThrows(
            ProtocolNotFoundException.class, () -> service.findByIdAndClientId(id, clientId));

    Assertions.assertEquals(404, ex.getError().getCode());
    Assertions.assertEquals("Protocol Not Found", ex.getError().getMessage());
  }

  @Test
  void shouldInvokeRepositoryWhenFindByIdAndClientId() {
    final var id = UUID.randomUUID();
    final var clientId = UUID.randomUUID();
    service.findByIdAndClientId(id.toString(), clientId);

    Mockito.verify(repository).findByIdAndClientId(id, clientId);
  }

  @Test
  void shouldSaveFile() {
    // Arrange
    final var file = new File();

    // Act
    service.save(file);

    // Assert
    Mockito.verify(repository).save(file);
  }

  @Test
  void shouldFindById() {
    // Act
    service.findById(UUID.randomUUID());

    // Assert
    Mockito.verify(repository, Mockito.times(1)).findById(Mockito.any(UUID.class));
  }

  @Test
  void shouldThrowNullPointerExceptionWhenFindByNullId() {
    // Act
    Assertions.assertThrows(NullPointerException.class, () -> service.findById(null));

    // Assert
    Mockito.verify(repository, Mockito.never()).findById(Mockito.any(UUID.class));
  }

  @Test
  void shouldThrowNullPointerExceptionWhenSaveNullFile() {
    // Act
    Assertions.assertThrows(NullPointerException.class, () -> service.save(null));

    // Assert
    Mockito.verify(repository, Mockito.never()).save(Mockito.any(File.class));
  }

  @Test
  void shouldSendToValidate() throws IOException {
    // Arrange
    final var fileId = UUID.randomUUID();
    final var clientId = UUID.randomUUID();
    final var companyId = UUID.randomUUID();
    final var file = new File();
    file.setId(fileId);
    file.setClientId(clientId);

    BlueMoonDomain.setDomain("onvio.com.br", "https");

    final InputStream content = IOUtils.toInputStream("file", "UTF-8");

    Mockito.doReturn(file).when(repository).save(file);
    Mockito.doReturn(companyId).when(userInfo).getCompanyId();

    // Act
    service.save(file, content);

    // Assert
    Mockito.verify(repository).save(file);
    Mockito.verify(s3Service).saveXml(content, FILE_FOLDER, clientId, companyId, "onvio.com.br");
  }

  @Test
  void shouldThrowNullPointerExceptionWhenSendToValidate() throws IOException {
    // Arrange
    InputStream content = IOUtils.toInputStream("file", "UTF-8");

    // Act
    Assertions.assertThrows(NullPointerException.class, () -> service.save(null, content));

    // Assert
    Mockito.verify(repository, Mockito.never()).save(Mockito.any(File.class));
  }
}
