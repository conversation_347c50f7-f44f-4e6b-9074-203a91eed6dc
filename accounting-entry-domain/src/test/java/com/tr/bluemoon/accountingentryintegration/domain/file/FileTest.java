package com.tr.bluemoon.accountingentryintegration.domain.file;

import java.time.LocalDateTime;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class FileTest {

  @Test
  void shouldUpdatePersistentEntityLastStatusOnWhenUpdateStatus() {
    // Arrange
    LocalDateTime lastStatusOn = LocalDateTime.of(2022, 1, 1, 12, 0, 0);

    final var file = new File();
    file.setId(UUID.randomUUID());
    file.setLastStatusOn(lastStatusOn);

    // Act
    file.setStatus(FileStatus.VALIDATED);

    // Assert
    Assertions.assertNotEquals(lastStatusOn, file.getLastStatusOn());
    Assertions.assertEquals(FileStatus.VALIDATED, file.getStatus());
  }

  @Test
  void shouldNotUpdateLastStatusOnWithSameStatus() {
    LocalDateTime lastStatusOn = LocalDateTime.of(2022, 1, 1, 12, 0, 0);

    // Arrange
    final var file = new File();
    file.setId(UUID.randomUUID());
    file.setLastStatusOn(lastStatusOn);

    // Act
    file.setStatus(FileStatus.AWAITING_VALIDATION);

    // Assert
    Assertions.assertEquals(lastStatusOn, file.getLastStatusOn());
    Assertions.assertEquals(FileStatus.AWAITING_VALIDATION, file.getStatus());
  }

  @Test
  void lastStatusOnShouldNotBeNull() {
    // Arrange
    final var file = new File();

    // Assert
    Assertions.assertNotNull(file.getLastStatusOn());
  }
}
