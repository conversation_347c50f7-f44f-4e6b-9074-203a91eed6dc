package com.tr.bluemoon.accountingentryintegration.domain.integration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;

import com.tr.bluemoon.accountingentryintegration.domain.cache.CacheManager;
import com.tr.bluemoon.bracct.commons.UserInfo;
import com.tr.bluemoon.bracct.commons.util.UuidUtils;
import java.util.UUID;
import javax.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

class ClientApiIntegrationDaoTest {

  @Mock private CacheManager cacheManager;

  @Mock private UserInfo userInfo;

  @Mock private EntityManager entityManager;

  @Spy @InjectMocks private ClientApiIntegrationDao dao;

  @BeforeEach
  public void setUp() {
    System.setProperty("kingfisher.client", "Partner Client ID");
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void shouldNotRemoveFromCacheWhenDeleting() {
    // Arrange
    ClientApiIntegration entity = new ClientApiIntegration();
    UUID companyId = UuidUtils.fromString("6372866D551545E68AF393635CB5AFDF");
    Mockito.doReturn(companyId).when(userInfo).getCompanyId();

    // Act
    dao.delete(entity);

    // Assert
    Mockito.verify(cacheManager, Mockito.never()).evict(any());
  }

  @Test
  void shouldGenerateTheCorrectKey() {
    // Arrange
    String integrationKey = "83gnjnvi4-fv";
    UUID companyId = UuidUtils.fromString("6372866D551545E68AF393635CB5AFDF");
    Mockito.doReturn(companyId).when(userInfo).getCompanyId();

    // Act
    String key = dao.getCacheKey(integrationKey);

    // Assert
    assertEquals(
        "BRInvoiceIntegrationServices:6372866d-5515-45e6-8af3-93635cb5afdf:clientApiIntegration:IntegrationKey:83gnjnvi4-fv",
        key);
    ;
  }
}
