package com.tr.bluemoon.accountingentryintegration.domain.integration;

import static org.mockito.Mockito.verify;

import com.tr.bluemoon.bracct.commons.util.UuidUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

class ClientApiIntegrationServiceTest {

  @Mock private ClientApiIntegrationDao dao;

  @Spy @InjectMocks private ClientApiIntegrationService service;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void shouldInvokeFindByIntegrationKeyInDao() {
    service.findClientApiIntegrationByIntegrationKey("integrationKey");

    verify(dao).findClientApiIntegrationByIntegrationKey("integrationKey");
  }

  private ClientApiIntegration createClientApiIntegration() {
    ClientApiIntegration entity = new ClientApiIntegration();
    entity.setId(UuidUtils.EMPTY_UUID);
    entity._persistence_set_companyId(UuidUtils.EMPTY_UUID);
    entity.setActivationKey("ABC-123");
    entity.setClient(createClient());
    entity.setClientId(UuidUtils.EMPTY_UUID);
    entity.setPartnerClientId("Partner Client ID");
    return entity;
  }

  private Client createClient() {
    Client client = new Client();
    client.setId(UuidUtils.EMPTY_UUID);
    client.setName("Client");
    client.setNationalIdentity("000.000.000-00");
    return client;
  }
}
