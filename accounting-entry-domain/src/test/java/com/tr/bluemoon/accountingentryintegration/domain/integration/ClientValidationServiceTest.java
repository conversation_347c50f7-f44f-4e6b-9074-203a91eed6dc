package com.tr.bluemoon.accountingentryintegration.domain.integration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.tr.bluemoon.accountingentryintegration.domain.file.FileStatusMessage;
import com.tr.bluemoon.bracct.commons.util.UuidUtils;
import java.util.UUID;
import javax.ws.rs.ForbiddenException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

class ClientValidationServiceTest {

  private static final String AN_EXCEPTION_SHOULD_BE_THROWN = "An exception should be thrown";
  private static final String NO_EXCEPTION_SHOULD_BE_THROWN = "No exception should be thrown";

  @InjectMocks private ClientValidationService service;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void shouldCheckClientIdDoesNotThrowExceptionWhenValid() {
    Assertions.assertDoesNotThrow(() -> service.checkClientId(UUID.randomUUID().toString()));
  }

  @Test
  void shouldCheckClientIdThrowExceptionWhenInvalid() {
    Assertions.assertThrows(
        ClientNotEnabledException.class, () -> service.checkClientId("invalidUuid"));
  }

  @Test
  void shouldReturnFalseWhenClientIdIsNull() {
    // Arrange
    String clientId = null;

    // Act
    boolean isValidUuid = service.isValidUuid(clientId);

    // Assert
    assertFalse(isValidUuid);
  }

  @Test
  void shouldReturnFalseWhenClientIdIsEmpty() {
    // Arrange
    String clientId = "";

    // Act
    boolean isValidUuid = service.isValidUuid(clientId);

    // Assert
    assertFalse(isValidUuid);
  }

  @Test
  void shouldReturnFalseWhenClientIdIsBlank() {
    // Arrange
    String clientId = " ";

    // Act
    boolean isValidUuid = service.isValidUuid(clientId);

    // Assert
    assertFalse(isValidUuid);
  }

  @Test
  void shouldReturnFalseWhenClientIdIsNotAValidUuid() {
    // Arrange
    String clientId = "notUuid";

    // Act
    boolean isValidUuid = service.isValidUuid(clientId);

    // Assert
    assertFalse(isValidUuid);
  }

  @Test
  void shouldReturnTrueWhenClientIdIsAValidUuid() {
    // Arrange
    String clientId = "00000000-0000-0000-0000-000000000000";

    // Act
    boolean isValidUuid = service.isValidUuid(clientId);

    // Assert
    assertTrue(isValidUuid);
  }

  @Test
  void shouldPassWhenClientApiIntegrationIsEnabled() {
    // Arrange
    String partnerId = "partnerId";
    ClientApiIntegration clientApiIntegration = new ClientApiIntegration();
    clientApiIntegration.setClientId(UUID.randomUUID());
    clientApiIntegration.setPartnerClientId(partnerId);

    // Act
    try {
      service.validate(clientApiIntegration, partnerId);
    } catch (ClientNotEnabledException ex) {
      // Assert
      fail(NO_EXCEPTION_SHOULD_BE_THROWN);
    }
  }

  @Test
  void shouldThrowWhenClientApiIntegrationClientIsNull() {
    // Arrange
    String partnerId = "partnerId";
    ClientApiIntegration clientApiIntegration = new ClientApiIntegration();
    clientApiIntegration.setClientId(null);
    clientApiIntegration.setPartnerClientId(partnerId);

    // Act
    try {
      service.validate(clientApiIntegration, partnerId);

      fail(AN_EXCEPTION_SHOULD_BE_THROWN);
    } catch (ClientNotEnabledException ex) {
      // Assert
      assertNotNull(ex);
      assertNotNull(ex.getError());
      assertEquals(400, ex.getError().getCode());
      assertNotNull(ex.getError().getErrors());
      assertEquals(1, ex.getError().getErrors().size());
      assertEquals(
          FileStatusMessage.E4.getMessage(), ex.getError().getErrors().get(0).getMessage());
    }
  }

  @Test
  void shouldThrowWhenClientApiIntegrationClientIsEmpty() {
    // Arrange
    String partnerId = "partnerId";
    ClientApiIntegration clientApiIntegration = new ClientApiIntegration();
    clientApiIntegration.setClientId(UuidUtils.EMPTY_UUID);
    clientApiIntegration.setPartnerClientId(partnerId);

    // Act
    try {
      service.validate(clientApiIntegration, partnerId);

      fail(AN_EXCEPTION_SHOULD_BE_THROWN);
    } catch (ClientNotEnabledException ex) {
      // Assert
      assertNotNull(ex);
      assertNotNull(ex.getError());
      assertEquals(400, ex.getError().getCode());
      assertNotNull(ex.getError().getErrors());
      assertEquals(1, ex.getError().getErrors().size());
      assertEquals(
          FileStatusMessage.E4.getMessage(), ex.getError().getErrors().get(0).getMessage());
    }
  }

  @Test
  void shouldThrowWhenPartnerDoesNotMatch() {
    // Arrange
    String partnerId = "partnerId";
    ClientApiIntegration clientApiIntegration = new ClientApiIntegration();
    clientApiIntegration.setClientId(UUID.randomUUID());
    clientApiIntegration.setPartnerClientId(partnerId);

    // Act
    try {
      service.validate(clientApiIntegration, "another partner");

      fail(AN_EXCEPTION_SHOULD_BE_THROWN);
    } catch (ForbiddenException ex) {
      // Assert
      assertNotNull(ex);
    }
  }

  @Test
  void shouldNoThrowWhenClientIsNotNull() {
    // Arrange
    Client client = new Client();

    // Act
    try {
      service.validateClientExists(client);

    } catch (ClientNotEnabledException ex) {
      // Assert
      fail(NO_EXCEPTION_SHOULD_BE_THROWN);
    }
  }

  @Test
  void shouldThrowWhenClientIsNull() {
    // Arrange
    // Act
    try {
      service.validateClientExists(null);

      fail(AN_EXCEPTION_SHOULD_BE_THROWN);
    } catch (ClientNotEnabledException ex) {
      // Assert
      assertNotNull(ex);
    }
  }
}
