package com.tr.bluemoon.accountingentryintegration.domain.integration;

import java.nio.charset.StandardCharsets;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ConverterUtilsTest {

  @Test
  void shouldEncode64() {
    final var phrase = "Hello World!";
    final var result = ConverterUtils.base64Encode(phrase.getBytes(StandardCharsets.UTF_8));
    Assertions.assertEquals("SGVsbG8gV29ybGQh", result);
  }

  @Test
  void shouldDecode64() throws Exception {
    final var encoded = "SGVsbG8gV29ybGQh";
    final var result = ConverterUtils.base64Decode(encoded);
    Assertions.assertEquals("Hello World!", new String(result, StandardCharsets.UTF_8));
  }

  @Test
  void shouldEncodeUuidAsBytes() {
    final var uuid = UUID.fromString("6467b4ae-3cd1-41a8-afa0-5990a7a6cbd5");
    final var result = ConverterUtils.asBytes(uuid);
    final var expected =
        new byte[] {100, 103, -76, -82, 60, -47, 65, -88, -81, -96, 89, -112, -89, -90, -53, -43};
    Assertions.assertArrayEquals(expected, result);
  }

  @Test
  void shouldDecodeBytesToUuid() {
    final var bytes =
        new byte[] {100, 103, -76, -82, 60, -47, 65, -88, -81, -96, 89, -112, -89, -90, -53, -43};
    final var result = ConverterUtils.asUuid(bytes);
    Assertions.assertEquals(UUID.fromString("6467b4ae-3cd1-41a8-afa0-5990a7a6cbd5"), result);
  }
}
