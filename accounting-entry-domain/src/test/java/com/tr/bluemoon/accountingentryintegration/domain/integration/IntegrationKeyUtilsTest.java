package com.tr.bluemoon.accountingentryintegration.domain.integration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.tr.bluemoon.bracct.commons.exception.BusinessException;
import com.tr.bluemoon.bracct.commons.util.UuidUtils;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class IntegrationKeyUtilsTest {

  @Test
  void shouldDecodeCompanyId() throws Exception {
    // Arrange
    String header = "QltCT6ZOTVi6RMoJ3C93_Gd-KBwfVky9iw44XxWNYZk";

    // Act
    UUID uuid = IntegrationKeyUtils.uuidDecode(header);

    // Assert
    assertEquals(createUUID(), uuid);
  }

  @Test
  void shouldEncodeCompanyId() throws Exception {
    // Arrange
    UUID uuid = createUUID();

    // Act
    String header = IntegrationKeyUtils.uuidEncode(uuid);

    // Assert
    assertTrue(header.contains("QltCT6ZOTVi6RMoJ3C93_"));
  }

  @Test
  void shouldEncodeToBase64() {
    // Arrange
    String content = "any content";

    // Act
    String base64Encoded = ConverterUtils.base64Encode(content.getBytes());

    // Assert
    assertEquals("YW55IGNvbnRlbnQ", base64Encoded);
  }

  @Test
  void shouldDecodeBase64() throws Exception {
    // Arrange
    String content = "YW55IGNvbnRlbnQ";

    // Act
    String base64Decoded = new String(ConverterUtils.base64Decode(content));

    // Assert
    assertEquals("any content", base64Decoded);
  }

  @Test
  void shouldValidateBase64IfLengthNotMatch() throws Exception {
    // Arrange
    String header = "any content";

    // Act
    Assertions.assertThrows(BusinessException.class, () -> IntegrationKeyUtils.uuidDecode(header));
  }

  @Test
  void shouldValidateInvalidUUIDSupplied() throws Exception {
    // Arrange
    String invalidHeader = "YWJjcWUxMjNkYXNkdnNkZoV-fsTepU2_iYYgWQd0Eps";

    // Act
    Assertions.assertThrows(
        BusinessException.class, () -> IntegrationKeyUtils.uuidDecode(invalidHeader));
  }

  @Test
  void shouldValidateUUIDPattern() {
    // Arrange
    UUID uuid = UuidUtils.EMPTY_UUID;

    // Act
    Assertions.assertThrows(BusinessException.class, () -> IntegrationKeyUtils.validateUUID(uuid));
  }

  @Test
  void shouldNotValidateUUIDPattern() {
    // Arrange
    UUID uuid = UUID.randomUUID();

    // Act
    IntegrationKeyUtils.validateUUID(uuid);
  }

  private UUID createUUID() {
    return UUID.fromString("425b424f-a64e-4d58-ba44-ca09dc2f77fc");
  }
}
