package com.tr.bluemoon.accountingentryintegration.domain.integration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

import com.tr.bluemoon.accountingentryintegration.domain.file.FileStatusMessage;
import com.tr.bluemoon.bracct.commons.UserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * {@link PartnerService} test.
 *
 * <AUTHOR>
 */
class PartnerServiceTest {

  private static final String PARTNER_NOT_INFORMED_MESSAGE = "Partner Not Informed";
  private static final String PARTNER_NOT_ENABLED_MESSAGE = "Partner Not Enabled";
  private static final String AN_EXCEPTION_SHOULD_BE_THROWN_MESSAGE =
      "An exception should be thrown.";
  private static final String NO_EXCEPTION_SHOULD_BE_THROWN_MESSAGE =
      "No exception should be thrown.";

  @Mock private UserInfo userInfo;

  @InjectMocks private PartnerService service;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void shouldPassWhenUserIsNotStaffEnabled() {
    // Arrange
    String partnerId = null;
    doReturn(false).when(userInfo).isStaffEnabled();

    // Act
    try {
      service.checkPartnerId(partnerId);
    } catch (Exception ex) {
      // Assert
      fail(NO_EXCEPTION_SHOULD_BE_THROWN_MESSAGE);
    }
  }

  @Test
  void shouldFailWhenPartnerIdIsNotAValidUuid() {
    // Arrange
    PartnerService spyService = spy(service);
    String partnerId = null;
    doReturn(true).when(userInfo).isStaffEnabled();
    doReturn(false).when(spyService).isValidUuid(partnerId);

    // Act
    try {
      spyService.checkPartnerId(partnerId);

      // Assert
      fail(AN_EXCEPTION_SHOULD_BE_THROWN_MESSAGE);
    } catch (PartnerNotInformedException ex) {
      // Assert
      assertNotNull(ex.getError());
      assertEquals(400, ex.getError().getCode());
      assertEquals(PARTNER_NOT_INFORMED_MESSAGE, ex.getError().getMessage());
      assertNotNull(ex.getError().getErrors());
      assertEquals(1, ex.getError().getErrors().size());
      assertEquals(FileStatusMessage.E5.name(), ex.getError().getErrors().get(0).getReason());
    }
  }

  @Test
  void shouldFailWhenPartnerIsNotEnabled() {
    // Arrange
    PartnerService spyService = spy(service);
    String partnerId = null;
    doReturn(true).when(userInfo).isStaffEnabled();
    doReturn(true).when(spyService).isValidUuid(partnerId);

    // Act
    try {
      spyService.checkPartnerId(partnerId);

      // Assert
      fail(AN_EXCEPTION_SHOULD_BE_THROWN_MESSAGE);
    } catch (PartnerNotEnabledException ex) {
      // Assert
      assertNotNull(ex.getError());
      assertEquals(400, ex.getError().getCode());
      assertEquals(PARTNER_NOT_ENABLED_MESSAGE, ex.getError().getMessage());
      assertNotNull(ex.getError().getErrors());
      assertEquals(1, ex.getError().getErrors().size());
      assertEquals(FileStatusMessage.E6.name(), ex.getError().getErrors().get(0).getReason());
    }
  }

  @Test
  void shouldPassWhenPartnerIsEnabled() {
    // Arrange
    PartnerService spyService = spy(service);
    String partnerId = "edd2b872-bc4c-4ed8-ac38-0628d55d55fe";
    doReturn(true).when(userInfo).isStaffEnabled();
    doReturn(true).when(spyService).isValidUuid(partnerId);

    // Act
    try {
      spyService.checkPartnerId(partnerId);
    } catch (Exception ex) {
      // Assert
      fail(NO_EXCEPTION_SHOULD_BE_THROWN_MESSAGE);
    }
  }

  @Test
  void shouldReturnFalseWhenPartnerIdIsNull() {
    // Arrange
    String partnerId = null;

    // Act
    boolean isValidUuid = service.isValidUuid(partnerId);

    // Assert
    assertFalse(isValidUuid);
  }

  @Test
  void shouldReturnFalseWhenPartnerIdIsEmpty() {
    // Arrange
    String partnerId = "";

    // Act
    boolean isValidUuid = service.isValidUuid(partnerId);

    // Assert
    assertFalse(isValidUuid);
  }

  @Test
  void shouldReturnFalseWhenPartnerIdIsBlank() {
    // Arrange
    String partnerId = " ";

    // Act
    boolean isValidUuid = service.isValidUuid(partnerId);

    // Assert
    assertFalse(isValidUuid);
  }

  @Test
  void shouldReturnFalseWhenPartnerIdIsNotAValidUuid() {
    // Arrange
    String partnerId = "notUuid";

    // Act
    boolean isValidUuid = service.isValidUuid(partnerId);

    // Assert
    assertFalse(isValidUuid);
  }

  @Test
  void shouldReturnTrueWhenPartnerIdIsAValidUuid() {
    // Arrange
    String partnerId = "00000000-0000-0000-0000-000000000000";

    // Act
    boolean isValidUuid = service.isValidUuid(partnerId);

    // Assert
    assertTrue(isValidUuid);
  }

  @Test
  void shouldReturnTrueWhenPartnerIdIsCorrect() {
    // Arrange
    String partnerId = "edd2b872-bc4c-4ed8-ac38-0628d55d55fe";

    // Act
    boolean isPartnerEnabled = service.isPartnerEnabled(partnerId);

    // Assert
    assertTrue(isPartnerEnabled);
  }

  @Test
  void shouldReturnFalseWhenPartnerIdIsIncorrect() {
    // Arrange
    String partnerId = "00000000-0000-0000-0000-000000000000";

    // Act
    boolean isPartnerEnabled = service.isPartnerEnabled(partnerId);

    // Assert
    assertFalse(isPartnerEnabled);
  }
}
