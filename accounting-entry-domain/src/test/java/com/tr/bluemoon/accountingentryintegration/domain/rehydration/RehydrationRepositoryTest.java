package com.tr.bluemoon.accountingentryintegration.domain.rehydration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import javax.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class RehydrationRepositoryTest {

  @Mock
  private EntityManager entityManager;

  @InjectMocks
  private RehydrationRepository rehydrationRepository;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void saveNewEntity() {
    Rehydration entity = mock(Rehydration.class);
    when(entity.isNew()).thenReturn(true);

    Rehydration result = rehydrationRepository.save(entity);

    verify(entityManager).persist(entity);
    assertEquals(entity, result);
  }

  @Test
  void saveExistingEntity() {
    Rehydration entity = mock(Rehydration.class);
    when(entity.isNew()).thenReturn(false);
    Rehydration mergedEntity = mock(Rehydration.class);
    when(entityManager.merge(entity)).thenReturn(mergedEntity);

    Rehydration result = rehydrationRepository.save(entity);

    verify(entityManager).merge(entity);
    assertEquals(mergedEntity, result);
  }
}