package com.tr.bluemoon.accountingentryintegration.domain.rehydration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

import com.tr.bluemoon.accountingentryintegration.domain.document.Document;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class RehydrationServiceTest {

  @Mock
  private RehydrationRepository repository;

  @InjectMocks
  private RehydrationService service;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void createRehydrationWithValidDocuments() {
    Document document = new Document();
    document.setId(UUID.randomUUID());
    document.setClientId(UUID.randomUUID());
    LocalDateTime offset = LocalDateTime.now();

    Rehydration rehydration = new Rehydration();
    rehydration.setId(UUID.randomUUID());
    rehydration.setOffsetDate(offset);
    RehydrationDocument rehydrationDocument = new RehydrationDocument();
    rehydrationDocument.setDocumentId(document.getId());
    rehydration.setDocuments(List.of(rehydrationDocument));
    List<Document> documents = List.of(document);

    when(repository.save(any(Rehydration.class))).thenReturn(rehydration);

    Rehydration result = service.create(documents, offset);

    assertNotNull(result);
    assertEquals(1, result.getDocuments().size());
    assertEquals(document.getId(), result.getDocuments().get(0).getDocumentId());
  }
}