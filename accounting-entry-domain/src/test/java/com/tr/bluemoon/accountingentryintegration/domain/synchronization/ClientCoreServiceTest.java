package com.tr.bluemoon.accountingentryintegration.domain.synchronization;

import com.tr.bluemoon.accountingentryintegration.domain.synchronization.dto.ClientV3Dto;
import com.tr.bluemoon.accountingentryintegration.domain.synchronization.dto.PagedClientsV3Dto;
import com.tr.bluemoon.accountingentryintegration.domain.synchronization.dto.SearchDto;
import com.tr.bluemoon.bracct.commons.UserInfo;
import com.tr.bluemoon.core.CoreServicesException;
import java.util.List;
import java.util.UUID;
import javax.persistence.NoResultException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

class ClientCoreServiceTest {

  private static final UUID companyId = UUID.fromString("fb4150bb-7f58-4f92-a4f5-c2b6d1e2b57a");
  private static final String CORE_CONTACT_EXPANDED =
      "primaryContactExpanded,primaryContactExpanded.nationalIdentitiesExpanded,primaryContactExpanded.contactDataExpanded,clientMainExpanded";
  private static final String CORE_FILTER_BY_ID_OP_EQ =
      "{'items':[{'by':'id','op':'EQ','value':'03877AAD181A494AB8A6DAA35D5000EA'}]}";

  @Mock private UserInfo userInfo;

  @Spy @InjectMocks private ClientCoreService service;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);

    Mockito.when(userInfo.getCompanyId()).thenReturn(companyId);
  }

  @Test
  void shouldGetClients() throws CoreServicesException {
    // Arrange
    final var searchBody = new SearchDto();

    Mockito.doReturn("https://test.onvio.com.br/api/core").when(service).getCoreServicesRoute();
    Mockito.doReturn(null)
        .when(service)
        .postToCoreServices(Mockito.any(), Mockito.anyString(), Mockito.any());

    // Act
    service.get(searchBody, companyId);

    Mockito.verify(service)
        .postToCoreServices(
            Mockito.eq(searchBody),
            Mockito.eq("FB4150BB7F584F92A4F5C2B6D1E2B57A"),
            Mockito.argThat(
                uri ->
                    uri.toString()
                        .equals(
                            "https://test.onvio.com.br/api/core/v3/companies/FB4150BB7F584F92A4F5C2B6D1E2B57A/clients/search")));
  }

  @Test
  void shouldGetClientById() throws CoreServicesException {
    // Arrange
    final var clientId = "03877AAD181A494AB8A6DAA35D5000EA";
    final var pagedClients = new PagedClientsV3Dto();
    final List<ClientV3Dto> items = List.of(new ClientV3Dto());

    pagedClients.setItems(items);
    pagedClients.setCurrentItemCount(1);

    Mockito.doReturn("https://test.onvio.com.br/api/core").when(service).getCoreServicesRoute();
    Mockito.doReturn(pagedClients)
        .when(service)
        .postToCoreServices(Mockito.any(), Mockito.anyString(), Mockito.any());

    // Act
    service.get(clientId);

    Mockito.verify(service)
        .postToCoreServices(
            Mockito.argThat(
                body ->
                    Boolean.TRUE.equals(body.getExcludeCount())
                        && CORE_CONTACT_EXPANDED.equals(body.getExpand())
                        && CORE_FILTER_BY_ID_OP_EQ.equals(body.getFilterSearchSort().getFilter())),
            Mockito.eq("FB4150BB7F584F92A4F5C2B6D1E2B57A"),
            Mockito.argThat(
                uri ->
                    uri.toString()
                        .equals(
                            "https://test.onvio.com.br/api/core/v3/companies/FB4150BB7F584F92A4F5C2B6D1E2B57A/clients/search")));
  }

  @Test
  void shouldThrowNoResultException() throws CoreServicesException {
    // Arrange
    final var clientId = "03877AAD181A494AB8A6DAA35D5000EA";
    final var pagedClients = new PagedClientsV3Dto();
    pagedClients.setCurrentItemCount(0);

    Mockito.doReturn("https://test.onvio.com.br/api/core").when(service).getCoreServicesRoute();
    Mockito.doReturn(pagedClients)
        .when(service)
        .postToCoreServices(Mockito.any(), Mockito.anyString(), Mockito.any());

    // Act
    Assertions.assertThrows(NoResultException.class, () -> service.get(clientId));

    Mockito.verify(service)
        .postToCoreServices(
            Mockito.argThat(
                body ->
                    Boolean.TRUE.equals(body.getExcludeCount())
                        && CORE_CONTACT_EXPANDED.equals(body.getExpand())
                        && CORE_FILTER_BY_ID_OP_EQ.equals(body.getFilterSearchSort().getFilter())),
            Mockito.eq("FB4150BB7F584F92A4F5C2B6D1E2B57A"),
            Mockito.argThat(
                uri ->
                    uri.toString()
                        .equals(
                            "https://test.onvio.com.br/api/core/v3/companies/FB4150BB7F584F92A4F5C2B6D1E2B57A/clients/search")));
  }
}
