package com.tr.bluemoon.accountingentryintegration.domain.synchronization;

import com.tr.bluemoon.accountingentryintegration.domain.integration.Client;
import com.tr.bluemoon.bracct.commons.util.UuidUtils;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class ClientServiceTest {

  @Mock private ClientDao dao;

  @InjectMocks private ClientService service;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void shouldThrowAnExceptionWhenIdTypedLikeUUIDIsNull() {
    // Arrange
    UUID id = null;

    // Act
    Assertions.assertThrows(NullPointerException.class, () -> service.requireIdNonNull(id));
  }

  @Test
  void shouldThrowAnExceptionWhenIdTypedLikeStringIsNull() {
    // Arrange
    String id = null;

    // Act
    Assertions.assertThrows(NullPointerException.class, () -> service.requireIdNonNull(id));
  }

  @Test
  void shouldFindByIdWithStringParameter() {
    // Arrange
    UUID id = UUID.randomUUID();
    Client client = new Client();

    ClientService spyService = Mockito.spy(this.service);

    Mockito.doReturn(client).when(dao).findById(id);

    // Act
    spyService.findById(UuidUtils.fromUUID(id));

    // Assert
    Mockito.verify(spyService).requireIdNonNull(id);
    Mockito.verify(dao).findById(id);
  }

  @Test
  void shouldFindById() {
    // Arrange
    UUID id = UUID.randomUUID();
    Client client = new Client();

    ClientService spyService = Mockito.spy(this.service);

    Mockito.doReturn(client).when(dao).findById(id);

    // Act
    spyService.findById(id);

    // Assert
    Mockito.verify(spyService).requireIdNonNull(id);
    Mockito.verify(dao).findById(id);
  }

  @Test
  void shouldDeleteClientById() {
    // Arrange
    UUID id = UUID.randomUUID();
    Client client = new Client();

    ClientService spyService = Mockito.spy(this.service);

    Mockito.doReturn(client).when(dao).findById(id);

    // Act
    spyService.delete(id);

    // Assert
    Mockito.verify(spyService).requireIdNonNull(id);
    Mockito.verify(dao).findById(id);
    Mockito.verify(dao).delete(client);
  }

  @Test
  void shouldSaveClient() {
    // Arrange
    Client client = new Client();

    Mockito.doReturn(client).when(dao).save(client);

    // Act
    service.save(client);

    // Assert
    Mockito.verify(dao, Mockito.times(1)).save(client);
  }

  @Test
  void shouldThrowAnExceptionWhenTryToSaveAndClientIsNull() {
    // Arrange
    Client client = null;

    // Act
    Assertions.assertThrows(NullPointerException.class, () -> service.save(client));
  }

  @Test
  void shouldThrowAnExceptionWhenTryToDeleteIdIsNull() {
    // Arrange
    UUID id = null;

    // Act
    Assertions.assertThrows(NullPointerException.class, () -> service.delete(id));
  }

  @Test
  void shouldFindByIdAndDeleteId() {
    // Arrange
    UUID id = UUID.randomUUID();
    Client client = new Client();
    client.setId(id);

    Mockito.doReturn(client).when(dao).findById(id);
    Mockito.doNothing().when(dao).delete(client);

    // Act
    service.delete(id);

    // Assert
    Mockito.verify(dao).findById(id);
    Mockito.verify(dao).delete(client);
  }
}
