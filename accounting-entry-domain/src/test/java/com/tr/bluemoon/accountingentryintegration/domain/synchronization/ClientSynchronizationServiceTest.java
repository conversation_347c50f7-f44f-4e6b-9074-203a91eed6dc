package com.tr.bluemoon.accountingentryintegration.domain.synchronization;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;

import com.tr.bluemoon.accountingentryintegration.domain.integration.Client;
import com.tr.bluemoon.accountingentryintegration.domain.integration.ClientNotEnabledException;
import com.tr.bluemoon.accountingentryintegration.domain.synchronization.dto.ClientV3Dto;
import com.tr.bluemoon.bracct.commons.exception.BusinessException;
import com.tr.bluemoon.bracct.commons.util.UuidUtils;
import com.tr.bluemoon.core.ClientMain;
import com.tr.bluemoon.core.ContactV4;
import com.tr.bluemoon.core.CoreServicesException;
import com.tr.bluemoon.core.NamedReference;
import com.tr.bluemoon.core.NationalIdentity;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import javax.persistence.NoResultException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class ClientSynchronizationServiceTest {

  private static final String MASKED_TEXT = "MASKED";
  private static final String CONTACT_ID_TEXT = "CONTACT_ID";

  @Mock private ClientCoreService clientCoreService;

  @Mock private ClientService clientService;

  @InjectMocks private ClientSynchronizationService service;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  void shouldThrowABusinessExceptionWhenANullPointerExceptionIsThrowed() {
    Assertions.assertThrows(BusinessException.class, () -> service.synchronizeByDto(null, null));
  }

  @Test
  void shouldSynchronizeByDto() {
    // Arrange
    ClientSynchronizationService spyService = spy(this.service);

    ClientV3Dto dto = new ClientV3Dto();
    dto.setId(UuidUtils.fromUUID(UUID.randomUUID()));

    Client entity = new Client();

    doReturn(entity).when(clientService).save(entity);
    doNothing().when(spyService).convertToEntity(entity, dto);

    // Act
    spyService.synchronizeByDto(dto, entity);

    // Assert
    verify(clientService).save(entity);
    verify(spyService).convertToEntity(entity, dto);
  }

  @Test
  void shouldSynchronizeAClientWhenNotFoundIt() {
    // Arrange
    String clientId = UuidUtils.fromUUID(UUID.randomUUID());
    Client entity = new Client();

    ClientSynchronizationService spyService = spy(this.service);

    doReturn(entity).when(spyService).synchronizeByClientId(any(Client.class));
    doThrow(NoResultException.class).when(clientService).findById(clientId);

    // Act
    Client result = spyService.findOrSynchronizeClientId(clientId);

    // Assert
    assertEquals(entity, result);
    verify(clientService).findById(clientId);
    verify(spyService).synchronizeByClientId(any(Client.class));
  }

  @Test
  void shouldThrowClientNotEnabledExceptionWhenTryToSynchronizeClientNotFound()
      throws CoreServicesException {
    // Arrange
    String clientId = UuidUtils.fromUUID(UUID.randomUUID());
    Client entity = new Client();
    ClientV3Dto dto = new ClientV3Dto();
    dto.setId(clientId);

    ClientSynchronizationService spyService = spy(this.service);

    doThrow(NoResultException.class).when(clientService).findById(clientId);
    doThrow(NoResultException.class).when(clientCoreService).get(clientId);

    // Act
    Assertions.assertThrows(
        ClientNotEnabledException.class, () -> spyService.findOrSynchronizeClientId(clientId));

    // Assert
    verify(clientCoreService, Mockito.times(1)).get(clientId);
    verify(clientService, Mockito.times(1)).findById(clientId);
    verify(clientService, Mockito.never()).save(Mockito.any(Client.class));
  }

  @Test
  void sshouldSynchronizeClientWhenGetFromCore() throws CoreServicesException {
    // Arrange
    String clientId = UuidUtils.fromUUID(UUID.randomUUID());
    Client entity = new Client();
    ClientV3Dto dto = new ClientV3Dto();
    dto.setId(clientId);

    ClientSynchronizationService spyService = spy(this.service);

    doThrow(NoResultException.class).when(clientService).findById(clientId);
    doReturn(dto).when(clientCoreService).get(clientId);

    // Act
    spyService.findOrSynchronizeClientId(clientId);

    // Assert
    verify(clientCoreService, Mockito.times(1)).get(clientId);
    verify(clientService, Mockito.times(1)).findById(clientId);
    verify(clientService, Mockito.times(1)).save(Mockito.any(Client.class));
  }

  @Test
  void shouldThrowABusinessExceptionWhenTryToFindClientOnCoreAndItThrowedANoResultException()
      throws NoResultException, CoreServicesException {
    // Arrange
    Client entity = new Client();
    entity.setId(UUID.randomUUID());
    String clientId = UuidUtils.fromUUID(entity.getId());

    doThrow(NoResultException.class).when(clientCoreService).get(clientId);

    // Act
    Assertions.assertThrows(BusinessException.class, () -> service.synchronizeByClientId(entity));
  }

  @Test
  void shouldThrowABusinessExceptionWhenTryToFindClientOnCoreAndItThrowedACoreServicesException()
      throws NoResultException, UnsupportedEncodingException, CoreServicesException {
    // Arrange
    Client entity = new Client();
    entity.setId(UUID.randomUUID());
    String clientId = UuidUtils.fromUUID(entity.getId());

    doThrow(CoreServicesException.class).when(clientCoreService).get(clientId);

    // Act
    Assertions.assertThrows(BusinessException.class, () -> service.synchronizeByClientId(entity));
  }

  @Test
  void shouldReturnTrueWhenIsCpf() {
    // Arrange
    NamedReference kind = new NamedReference();
    kind.setId(ClientSynchronizationService.CPF);

    NationalIdentity identity = new NationalIdentity();
    identity.setMasked(MASKED_TEXT);
    identity.setKind(kind);

    // Act
    boolean result = service.isBrIdentityInscription(identity);

    // Assert
    assertTrue(result);
  }

  @Test
  void shouldReturnTrueWhenIsCnpj() {
    // Arrange
    NamedReference kind = new NamedReference();
    kind.setId(ClientSynchronizationService.CNPJ);

    NationalIdentity identity = new NationalIdentity();
    identity.setMasked(MASKED_TEXT);
    identity.setKind(kind);

    // Act
    boolean result = service.isBrIdentityInscription(identity);

    // Assert
    assertTrue(result);
  }

  @Test
  void shouldReturnTrueWhenIsCei() {
    // Arrange
    NamedReference kind = new NamedReference();
    kind.setId(ClientSynchronizationService.CEI);

    NationalIdentity identity = new NationalIdentity();
    identity.setMasked(MASKED_TEXT);
    identity.setKind(kind);

    // Act
    boolean result = service.isBrIdentityInscription(identity);

    // Assert
    assertTrue(result);
  }

  @Test
  void shouldReturnTrueWhenIsCaepf() {
    // Arrange
    NamedReference kind = new NamedReference();
    kind.setId(ClientSynchronizationService.CAEPF);

    NationalIdentity identity = new NationalIdentity();
    identity.setMasked(MASKED_TEXT);
    identity.setKind(kind);

    // Act
    boolean result = service.isBrIdentityInscription(identity);

    // Assert
    assertTrue(result);
  }

  @Test
  void shouldReturnFalseWhenIsNotValidType() {
    // Arrange
    NamedReference kind = new NamedReference();
    kind.setId("Other");

    NationalIdentity identity = new NationalIdentity();
    identity.setMasked(MASKED_TEXT);
    identity.setKind(kind);

    // Act
    boolean result = service.isBrIdentityInscription(identity);

    // Assert
    assertFalse(result);
  }

  @Test
  void shouldReturnFalseWhenMaskedValueIsNull() {
    // Arrange
    NamedReference kind = new NamedReference();
    kind.setId(ClientSynchronizationService.CNPJ);

    NationalIdentity identity = new NationalIdentity();
    identity.setKind(kind);

    // Act
    boolean result = service.isBrIdentityInscription(identity);

    // Assert
    assertFalse(result);
  }

  @Test
  void shouldReturnFalseWhenMaskedValueIsEmpty() {
    // Arrange
    NamedReference kind = new NamedReference();
    kind.setId(ClientSynchronizationService.CNPJ);

    NationalIdentity identity = new NationalIdentity();
    identity.setMasked("");
    identity.setKind(kind);

    // Act
    boolean result = service.isBrIdentityInscription(identity);

    // Assert
    assertFalse(result);
  }

  @Test
  void shouldRemoveMaskFromCpfValue() {
    // Arrange
    String expected = "80772147035";

    // Act
    String result = service.removeNonNumericCharacters("807.721.470-35");

    // Assert
    assertEquals(expected, result);
  }

  @Test
  void shouldRemoveMaskFromCnpjValue() {
    // Arrange
    String expected = "31498417000150";

    // Act
    String result = service.removeNonNumericCharacters("31.498.417/0001-50");

    // Assert
    assertEquals(expected, result);
  }

  @Test
  void shouldRemoveMaskFromCaepfValue() {
    // Arrange
    String expected = "12345678900123";

    // Act
    String result = service.removeNonNumericCharacters("123.456.789/001-23");

    // Assert
    assertEquals(expected, result);
  }

  @Test
  void shouldConvertClientV3DtoToEntity() {
    // Arrange
    final String expectedNationalIdentity = "02966458000125";

    NamedReference kind = new NamedReference();
    kind.setId(ClientSynchronizationService.CPF);
    NationalIdentity nationalIdentity = new NationalIdentity();
    nationalIdentity.setMasked("02.966.458/0001-25");
    nationalIdentity.setKind(kind);

    final List<NationalIdentity> nationalIdentities = new ArrayList<>();
    nationalIdentities.add(nationalIdentity);

    ContactV4 contact = new ContactV4();
    contact.setNationalIdentitiesExpanded(nationalIdentities);

    NamedReference status = new NamedReference();
    status.setId("ACTIVE");

    ClientMain clientMain = new ClientMain();
    clientMain.setStatus(status);

    ClientV3Dto dto = new ClientV3Dto();
    dto.setId(UuidUtils.fromUUID(UUID.randomUUID()));
    dto.setCode("CODE");
    dto.setName("NAME");
    dto.setClientMainExpanded(clientMain);
    dto.setPrimaryContactExpanded(contact);

    Client entity = new Client();

    // Act
    service.convertToEntity(entity, dto);

    // Assert
    assertEquals(dto.getCode(), entity.getCode());
    assertEquals(dto.getName(), entity.getName());
    assertFalse(entity.isEnabled());
    assertNotNull(entity.getId());
    assertEquals(expectedNationalIdentity, entity.getNationalIdentity());
    assertEquals(ClientSynchronizationService.CPF, entity.getNationalIdentityType());
  }

  @Test
  void shouldConvertClientV3DtoToEntityButDoNotChangeEnabled() {
    // Arrange
    final String expectedNationalIdentity = "95525191000111";

    NamedReference kind = new NamedReference();
    kind.setId(ClientSynchronizationService.CNPJ);
    NationalIdentity nationalIdentity = new NationalIdentity();
    nationalIdentity.setMasked("95.525.191/0001-11");
    nationalIdentity.setKind(kind);

    final List<NationalIdentity> nationalIdentities = new ArrayList<>();
    nationalIdentities.add(nationalIdentity);

    ContactV4 contact = new ContactV4();
    contact.setNationalIdentitiesExpanded(nationalIdentities);

    NamedReference status = new NamedReference();
    status.setId("ACTIVE");

    ClientMain clientMain = new ClientMain();
    clientMain.setStatus(status);

    ClientV3Dto dto = new ClientV3Dto();
    dto.setId(UuidUtils.fromUUID(UUID.randomUUID()));
    dto.setCode("CODE");
    dto.setName("NAME");
    dto.setClientMainExpanded(clientMain);
    dto.setPrimaryContactExpanded(contact);

    Client entity = new Client();
    entity.setEnabled(true);

    // Act
    service.convertToEntity(entity, dto);

    // Assert
    assertEquals(dto.getCode(), entity.getCode());
    assertEquals(dto.getName(), entity.getName());
    assertTrue(entity.isEnabled());
    assertNotNull(entity.getId());
    assertEquals(expectedNationalIdentity, entity.getNationalIdentity());
    assertEquals(ClientSynchronizationService.CNPJ, entity.getNationalIdentityType());
  }

  @Test
  void shouldSetNationalIdentityToNullWhenItIsNotPresent() {
    // Arrange
    final ContactV4 contact = new ContactV4();

    NamedReference status = new NamedReference();
    status.setId("ACTIVE");

    ClientMain clientMain = new ClientMain();
    clientMain.setStatus(status);

    ClientV3Dto dto = new ClientV3Dto();
    dto.setId(UuidUtils.fromUUID(UUID.randomUUID()));
    dto.setCode("CODE");
    dto.setName("NAME");
    dto.setClientMainExpanded(clientMain);
    dto.setPrimaryContactExpanded(contact);

    Client entity = new Client();
    entity.setNationalIdentity("35321453000102");
    entity.setNationalIdentityType(ClientSynchronizationService.CPF);

    // Act
    service.convertToEntity(entity, dto);

    // Assert
    assertNull(entity.getNationalIdentity());
    assertNull(entity.getNationalIdentityType());
  }

  @Test
  void shouldSetNationalIdentityToNullWhenNationaldentityTypeIsNotValidType() {
    // Arrange
    NamedReference kind = new NamedReference();
    kind.setId("Other");
    NationalIdentity nationalIdentity = new NationalIdentity();
    nationalIdentity.setMasked("35.321.453/0001-02");
    nationalIdentity.setKind(kind);

    final List<NationalIdentity> nationalIdentities = new ArrayList<>();
    nationalIdentities.add(nationalIdentity);

    ContactV4 contact = new ContactV4();
    contact.setNationalIdentitiesExpanded(nationalIdentities);

    NamedReference status = new NamedReference();
    status.setId("ACTIVE");

    ClientMain clientMain = new ClientMain();
    clientMain.setStatus(status);

    ClientV3Dto dto = new ClientV3Dto();
    dto.setId(UuidUtils.fromUUID(UUID.randomUUID()));
    dto.setCode("CODE");
    dto.setName("NAME");
    dto.setClientMainExpanded(clientMain);
    dto.setPrimaryContactExpanded(contact);

    Client entity = new Client();

    // Act
    service.convertToEntity(entity, dto);

    // Assert
    assertEquals(dto.getCode(), entity.getCode());
    assertEquals(dto.getName(), entity.getName());
    assertFalse(entity.isEnabled());
    assertNotNull(entity.getId());
    assertNull(entity.getNationalIdentity());
    assertNull(entity.getNationalIdentityType());
  }

  @Test
  void shouldSetNationalIdentityToNullWhenPrimaryContactExpandedIsNull() {
    // Arrange
    ClientV3Dto dto = new ClientV3Dto();
    dto.setId(UuidUtils.fromUUID(UUID.randomUUID()));
    dto.setCode("CODE");
    dto.setName("NAME");

    Client entity = new Client();
    entity.setNationalIdentity("35321453000102");
    entity.setNationalIdentityType(ClientSynchronizationService.CPF);

    // Act
    service.convertToEntity(entity, dto);

    // Assert
    assertNull(entity.getNationalIdentity());
    assertNull(entity.getNationalIdentityType());
  }
}
