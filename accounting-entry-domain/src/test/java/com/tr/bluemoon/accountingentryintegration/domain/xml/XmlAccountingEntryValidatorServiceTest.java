package com.tr.bluemoon.accountingentryintegration.domain.xml;

import static javax.xml.XMLConstants.W3C_XML_SCHEMA_NS_URI;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.tr.bluemoon.accountingentryintegration.domain.file.FileStatus;
import com.tr.bluemoon.accountingentryintegration.domain.file.FileValidatorException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Locale;
import javax.xml.transform.Source;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.xml.sax.SAXException;

class XmlAccountingEntryValidatorServiceTest {

  static final String XSD = "/xml/accounting-entry.xsd";

  XmlAccountingEntryValidatorService validator;

  @BeforeEach
  void setUp() throws SAXException {
    Locale.setDefault(Locale.US);
    validator = new XmlAccountingEntryValidatorService(createSchema());
  }

  Schema createSchema() throws SAXException {
    Source schemaFile = new StreamSource(getResource(XSD));
    SchemaFactory factory = SchemaFactory.newInstance(W3C_XML_SCHEMA_NS_URI);
    return factory.newSchema(schemaFile);
  }

  InputStream getResource(String name) {
    return getClass().getResourceAsStream(name);
  }

  @Test
  void shouldSuccess() {
    InputStream stream = getResource("/xml/accounting-entry-success.xml");
    validator.validateXml(stream);
  }

  @Test
  void shouldSuccessWithoutConta() {
    InputStream stream = getResource("/xml/accounting-entry-success-without-conta.xml");
    validator.validateXml(stream);
  }

  @Test
  void shouldSuccessWithoutHistorico() {
    InputStream stream = getResource("/xml/accounting-entry-success-without-historico.xml");
    validator.validateXml(stream);
  }

  @Test
  void shouldSuccessWithoutContaAndHistorico() {
    InputStream stream = getResource("/xml/accounting-entry-success-without-conta-historico.xml");
    validator.validateXml(stream);
  }

  @Test
  void shouldThrowExceptionWhenCnpjIsInvalid() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-invalid-cnpj.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-pattern-valid: Value '95.540.477/0001-76' is not facet-valid with respect to pattern '([0-9]{2}[0-9]{3}[0-9]{3}[0-9]{4}[0-9]{2})' for type '#AnonType_cnpjinscricaoidentificacaolancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenCpfIsInvalid() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-invalid-cpf.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-pattern-valid: Value '679.911.760-45' is not facet-valid with respect to pattern '([0-9]{3}[0-9]{3}[0-9]{3}[0-9]{2})' for type '#AnonType_cpfinscricaoidentificacaolancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @ParameterizedTest
  @ValueSource(
      strings = {
        "/xml/accounting-entry-invalid-codigoconta.xml",
        "/xml/accounting-entry-invalid-codigohistorico.xml",
        "/xml/accounting-entry-invalid-filial.xml",
        "/xml/accounting-entry-invalid-codhistorico.xml",
        "/xml/accounting-entry-invalid-debito.xml",
        "/xml/accounting-entry-invalid-debito-ccusto.xml",
        "/xml/accounting-entry-invalid-credito.xml",
        "/xml/accounting-entry-invalid-credito-ccusto.xml"
      })
  void shouldThrowExceptionWhenIntegerIsInvalid(String xml) throws IOException {
    try (InputStream stream = getResource(xml)) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected = "cvc-datatype-valid.1.2.1: 'S' is not a valid value for 'integer'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenClassificacaoIsInvalid() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-invalid-classificacao.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-datatype-valid.1.2.1: '110100100S' is not a valid value for 'integer'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenTipoContaIsInvalid() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-invalid-tipo-conta.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-enumeration-valid: Value '1' is not facet-valid with respect to enumeration '[A, S]'. It must be a value from the enumeration.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @ParameterizedTest
  @ValueSource(
      strings = {
        "/xml/accounting-entry-invalid-datacadastro.xml",
        "/xml/accounting-entry-invalid-datainativacao.xml",
        "/xml/accounting-entry-invalid-data-lancamento.xml"
      })
  void shouldThrowExceptionWhenDateIsInvalid(String xml) throws IOException {
    try (InputStream stream = getResource(xml)) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected = "cvc-datatype-valid.1.2.1: '2020-05' is not a valid value for 'date'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenTipoLoteIsInvalid() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-invalid-tipo-lote.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-enumeration-valid: Value 'A' is not facet-valid with respect to enumeration '[X, V]'. It must be a value from the enumeration.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenSituacaoContaIsInvalid() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-invalid-situacao-conta.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-enumeration-valid: Value '1' is not facet-valid with respect to enumeration '[A, I]'. It must be a value from the enumeration.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @ParameterizedTest
  @ValueSource(
      strings = {
        "/xml/accounting-entry-invalid-valor.xml",
        "/xml/accounting-entry-invalid-debito-valorcc.xml",
        "/xml/accounting-entry-invalid-credito-valorcc.xml"
      })
  void shouldThrowExceptionWhenDecimalIsInvalid(String xml) throws IOException {
    try (InputStream stream = getResource(xml)) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected = "cvc-datatype-valid.1.2.1: '100,00' is not a valid value for 'decimal'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenRazaosocialIsLarger() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-large-razaosocial.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-maxLength-valid: Value 'EMPRESA TESTEEEEEEEEEEEEEEEEEEEEEEEEEEEEE' with length = '41' is not facet-valid with respect to maxLength '40' for type '#AnonType_razaosocialidentificacaolancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenCodigoContaIsLarger() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-large-codigoconta.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-maxInclusive-valid: Value '********' is not facet-valid with respect to maxInclusive '9999999' for type '#AnonType_codigocontacontalancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenCodigoContaIsSmaller() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-smaller-codigoconta.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-minInclusive-valid: Value '0' is not facet-valid with respect to minInclusive '1' for type '#AnonType_codigocontacontalancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenClassificacaoIsLarger() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-large-classificacao.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-maxInclusive-valid: Value '**********00000000000' is not facet-valid with respect to maxInclusive '******************99' for type '#AnonType_classificacaocontalancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenClassificacaoIsSmaller() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-smaller-classificacao.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-minInclusive-valid: Value '0' is not facet-valid with respect to minInclusive '1' for type '#AnonType_classificacaocontalancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenDescricaoContaIsLarger() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-large-conta-descricao.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-maxLength-valid: Value 'Lorem ipsum urna cras himenaeos tempor ante sodales, platea pharetra aenean phasellus ultricies sed, aptent quis nibh litora aptent risus risus risusss' with length = '151' is not facet-valid with respect to maxLength '150' for type '#AnonType_descricaocontalancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenCodigoHistoricoIsLarger() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-large-codigohistorico.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-maxInclusive-valid: Value '********' is not facet-valid with respect to maxInclusive '9999999' for type '#AnonType_codigohistoricohistoricolancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenCodigoHistoricoIsSmaller() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-smaller-codigohistorico.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-minInclusive-valid: Value '0' is not facet-valid with respect to minInclusive '1' for type '#AnonType_codigohistoricohistoricolancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenDescricaoHistoricoIsLarger() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-large-historico-descricao.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-maxLength-valid: Value 'Lorem ipsum urna cras himenaeos tempor ante sodales, platea pharetra aenean phasellus ultricies seddd' with length = '101' is not facet-valid with respect to maxLength '100' for type '#AnonType_descricaohistoricolancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenDebitoIsLarger() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-large-debito.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-maxInclusive-valid: Value '********' is not facet-valid with respect to maxInclusive '9999999' for type '#AnonType_debitolancamentolotelancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenDebitoIsSmaller() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-smaller-debito.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-minInclusive-valid: Value '0' is not facet-valid with respect to minInclusive '1' for type '#AnonType_debitolancamentolotelancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenCreditoIsLarger() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-large-credito.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-maxInclusive-valid: Value '********' is not facet-valid with respect to maxInclusive '9999999' for type '#AnonType_creditolancamentolotelancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenCreditoIsSmaller() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-smaller-credito.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-minInclusive-valid: Value '0' is not facet-valid with respect to minInclusive '1' for type '#AnonType_creditolancamentolotelancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenCodHistoricoIsLarger() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-large-codhistorico.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-maxInclusive-valid: Value '********' is not facet-valid with respect to maxInclusive '9999999' for type '#AnonType_codhistoricolancamentolotelancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenCodHistoricoIsSmaller() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-smaller-codhistorico.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-minInclusive-valid: Value '0' is not facet-valid with respect to minInclusive '1' for type '#AnonType_codhistoricolancamentolotelancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenLancamentoHistoricoIsLarger() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-large-lancamento-historico.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-maxLength-valid: Value 'Lorem ipsum venenatis nec purus aenean curabitur et venenatis, dui pharetra iaculis lectus litora ad nisi primis dui, at aliquet sollicitudin nisl consectetur diam tortor. quisque dictum metus egestas rutrum per himenaeos curae felis non arcu at vehicula, nisi porttitor pretium ad aenean amet arcu potenti diam et rhoncus. ornare turpis fermentum condimentum praesent viverra lacinia cras, condimentum rutrum leo orci torquent molestie praesent, a lorem libero sed adipiscing malesuada. placerat ornare fames tortor sed hendrerit platea turpis tortor, venenatis commodo dictumst netus sodales at vestibulum ac, volutpat quis sem pretium conubia duis fermentum. Suspendisse ultricies tristique netus rhoncus aptent, platea leo nisl pretium sollicitudin posuere, cursus aliquam lacinia accumsan. ultrices amet cursus tortor enim ante, commodo non vitae morbi. Lorem ipsum venenatis nec purus aenean curabitur et venenatis, dui pharetra iaculis lectus litora ad nisi primis dui, at aliquet sollicitudin.' with length = '1001' is not facet-valid with respect to maxLength '1000' for type '#AnonType_historicolancamentolotelancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @ParameterizedTest
  @ValueSource(
      strings = {
        "/xml/accounting-entry-large-valor.xml",
        "/xml/accounting-entry-large-debito-valorcc.xml",
        "/xml/accounting-entry-large-credito-valorcc.xml"
      })
  void shouldThrowExceptionWhenDecimalIsLarger(String xml) throws IOException {
    try (InputStream stream = getResource(xml)) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-totalDigits-valid: Value '**********10.00' has 12 total digits, but the number of total digits has been limited to 11.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenCcustoDebitoIsLarger() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-large-debito-ccusto.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-maxInclusive-valid: Value '**********' is not facet-valid with respect to maxInclusive '*********' for type '#AnonType_ccustoccustodebitolancamentolotelancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenCcustoDebitoIsSmaller() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-smaller-debito-ccusto.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-minInclusive-valid: Value '0' is not facet-valid with respect to minInclusive '1' for type '#AnonType_ccustoccustodebitolancamentolotelancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenCcustoCreditoIsLarger() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-large-credito-ccusto.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-maxInclusive-valid: Value '**********' is not facet-valid with respect to maxInclusive '*********' for type '#AnonType_ccustoccustocreditolancamentolotelancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenCcustoCreditoIsSmaller() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-smaller-credito-ccusto.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-minInclusive-valid: Value '0' is not facet-valid with respect to minInclusive '1' for type '#AnonType_ccustoccustocreditolancamentolotelancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenRazaoSocialIsAbsent() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-required-razaosocial.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-complex-type.2.4.b: The content of element 'identificacao' is not complete. One of '{razaosocial}' is expected.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenRazaoSocialIsEmpty() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-required-empty-razaosocial.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-minLength-valid: Value '' with length = '0' is not facet-valid with respect to minLength '1' for type '#AnonType_razaosocialidentificacaolancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @ParameterizedTest
  @ValueSource(
      strings = {
        "/xml/accounting-entry-required-empty-codigoconta.xml",
        "/xml/accounting-entry-required-empty-classificacao.xml",
        "/xml/accounting-entry-required-empty-codigohistorico.xml"
      })
  void shouldThrowExceptionWhenIntegerIsEmpty(String xml) throws IOException {
    try (InputStream stream = getResource(xml)) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected = "cvc-datatype-valid.1.2.1: '' is not a valid value for 'integer'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenCodigoContaIsAbsent() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-required-codigoconta.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-complex-type.2.4.a: Invalid content was found starting with element 'classificacao'. One of '{codigoconta}' is expected.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenClassificacaoIsAbsent() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-required-classificacao.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-complex-type.2.4.a: Invalid content was found starting with element 'tipo'. One of '{classificacao}' is expected.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenTipoIsAbsent() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-required-tipo.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-complex-type.2.4.a: Invalid content was found starting with element 'descricao'. One of '{tipo}' is expected.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenDescricaoContaIsAbsent() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-required-conta-descricao.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-complex-type.2.4.a: Invalid content was found starting with element 'datacadastro'. One of '{descricao}' is expected.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenDescricaoContaIsEmpty() throws IOException {
    try (InputStream stream =
        getResource("/xml/accounting-entry-required-empty-conta-descricao.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-minLength-valid: Value '' with length = '0' is not facet-valid with respect to minLength '1' for type '#AnonType_descricaocontalancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenDataCadastroIsAbsent() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-required-datacadastro.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-complex-type.2.4.a: Invalid content was found starting with element 'situacao'. One of '{datacadastro}' is expected.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenDescricaoHistoricoIsAbsent() throws IOException {
    try (InputStream stream =
        getResource("/xml/accounting-entry-required-historico-descricao.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-complex-type.2.4.b: The content of element 'historico' is not complete. One of '{descricao}' is expected.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenDescricaoHistoricoIsEmpty() throws IOException {
    try (InputStream stream =
        getResource("/xml/accounting-entry-required-empty-historico-descricao.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-minLength-valid: Value '' with length = '0' is not facet-valid with respect to minLength '1' for type '#AnonType_descricaohistoricolancamentoscontabeis'.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenTipoLoteIsAbsent() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-required-lote-tipo.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-complex-type.2.4.a: Invalid content was found starting with element 'data'. One of '{tipo}' is expected.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenDataLoteIsAbsent() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-required-lote-data.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-complex-type.2.4.a: Invalid content was found starting with element 'filial'. One of '{data}' is expected.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }

  @Test
  void shouldThrowExceptionWhenValorLancamentoIsAbsent() throws IOException {
    try (InputStream stream = getResource("/xml/accounting-entry-required-lancamento-valor.xml")) {
      FileValidatorException exc =
          assertThrows(FileValidatorException.class, () -> validator.validateXml(stream));
      String expected =
          "cvc-complex-type.2.4.a: Invalid content was found starting with element 'ccustodebito'. One of '{valor}' is expected.";

      assertEquals(expected, exc.getError().getErrors().get(1).getMessage());
      assertEquals(FileStatus.INVALID_STRUCTURE, exc.getStatus());
    }
  }
}
