<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
    <xs:element name="lancamentoscontabeis">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="identificacao">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="inscricao">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="cnpj" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="([0-9]{2}[0-9]{3}[0-9]{3}[0-9]{4}[0-9]{2})"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="cpf" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="([0-9]{3}[0-9]{3}[0-9]{3}[0-9]{2})"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="razaosocial">
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                        <xs:minLength value="1"/>
                                        <xs:maxLength value="40"/>
                                        <xs:whiteSpace value="collapse"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="conta" minOccurs="0">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="codigoconta">
                                <xs:simpleType>
                                    <xs:restriction base="xs:int">
                                        <xs:minInclusive value="1"/>
                                        <xs:maxInclusive value="9999999"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="classificacao">
                                <xs:simpleType>
                                    <xs:restriction base="xs:integer">
                                        <xs:minInclusive value="1"/>
                                        <xs:maxInclusive value="99999999999999999999"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="tipo">
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                        <xs:maxLength value="1"/>
                                        <xs:enumeration value="A"/>
                                        <xs:enumeration value="S"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="descricao">
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                        <xs:minLength value="1"/>
                                        <xs:maxLength value="150"/>
                                        <xs:whiteSpace value="collapse"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="datacadastro" type="xs:date"/>
                            <xs:element name="situacao" minOccurs="0">
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                        <xs:maxLength value="1"/>
                                        <xs:enumeration value="A"/>
                                        <xs:enumeration value="I"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="datainativacao" type="xs:date" minOccurs="0"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="historico" minOccurs="0">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="codigohistorico">
                                <xs:simpleType>
                                    <xs:restriction base="xs:int">
                                        <xs:minInclusive value="1"/>
                                        <xs:maxInclusive value="9999999"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="descricao">
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                        <xs:minLength value="1"/>
                                        <xs:maxLength value="100"/>
                                        <xs:whiteSpace value="collapse"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="lote" maxOccurs="unbounded">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="tipo">
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                        <xs:maxLength value="1"/>
                                        <xs:enumeration value="X"/>
                                        <xs:enumeration value="V"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="data" type="xs:date"/>
                            <xs:element name="filial" minOccurs="0">
                                <xs:simpleType>
                                    <xs:restriction base="xs:int">
                                        <xs:minInclusive value="1"/>
                                        <xs:maxInclusive value="9999999"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="lancamento" maxOccurs="unbounded">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="debito" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:restriction base="xs:int">
                                                    <xs:minInclusive value="1"/>
                                                    <xs:maxInclusive value="9999999"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="credito" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:restriction base="xs:int">
                                                    <xs:minInclusive value="1"/>
                                                    <xs:maxInclusive value="9999999"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="codhistorico" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:restriction base="xs:int">
                                                    <xs:minInclusive value="1"/>
                                                    <xs:maxInclusive value="9999999"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="historico" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:restriction base="xs:string">
                                                    <xs:maxLength value="1000"/>
                                                    <xs:whiteSpace value="collapse"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="valor" type="decimalType"/>
                                        <xs:element name="ccustodebito" minOccurs="0" maxOccurs="unbounded">
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="ccusto" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:restriction base="xs:int">
                                                                <xs:minInclusive value="1"/>
                                                                <xs:maxInclusive value="999999999"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="valorcc" type="decimalType" minOccurs="0"/>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="ccustocredito" minOccurs="0" maxOccurs="unbounded">
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="ccusto" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:restriction base="xs:int">
                                                                <xs:minInclusive value="1"/>
                                                                <xs:maxInclusive value="999999999"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="valorcc" type="decimalType" minOccurs="0"/>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
            <xs:attribute type="xs:float" name="versao"/>
        </xs:complexType>
    </xs:element>
    <xs:simpleType name="decimalType">
        <xs:restriction base="xs:decimal">
            <xs:totalDigits value="11"/>
            <xs:fractionDigits value="2"/>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
