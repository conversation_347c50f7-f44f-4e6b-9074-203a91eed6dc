# RabbitMQ Connection
RabbitMQHostName=onviopostgres.int.thomsonreuters.com
RabbitMQVirtualHostName=BlueMoon
RabbitMQPort=5671
RabbitMQUserName=bluemoonci
RabbitMQPassword=east

# Properties for local development
http.proxyHost=trta-prof-devops-squid.int.thomsonreuters.com
http.proxyPort=3128

#BmServices DEMO P5aOQy80
bmservices_key=46jgS6UF

# Consumers
brtap.accountingentryintegration.helloworld.concurrency=1
brtap.accountingentryintegration.helloworld.prefetch=1
brtap.accountingentryintegration.file-received.concurrency=1
brtap.accountingentryintegration.file-received.prefetch=1

#
# Connection Strings, see: http://bluemoonwiki.int.thomsonreuters.com/index.php/Connection_Strings
#
bucket_datasource=bm_br_acct_accounting_entry_integration_lookup_pg

# Bucket lookup database
bm_br_acct_accounting_entry_integration_lookup_pg.jdbc.driverClassName=org.postgresql.Driver
bm_br_acct_accounting_entry_integration_lookup_pg.jdbc.dataSourceClassName=org.postgresql.ds.PGSimpleDataSource
bm_br_acct_accounting_entry_integration_lookup_pg.jdbc.datasource.user=bm_br_acct_accounting_entry_integration_lookup_user
bm_br_acct_accounting_entry_integration_lookup_pg.jdbc.password=password
bm_br_acct_accounting_entry_integration_lookup_pg.jdbc.datasource.serverName=onviopostgres.int.thomsonreuters.com
bm_br_acct_accounting_entry_integration_lookup_pg.jdbc.datasource.portNumber=5432
bm_br_acct_accounting_entry_integration_lookup_pg.jdbc.datasource.databaseName=bm_br_acct_accounting_entry_integration_lookup
bm_br_acct_accounting_entry_integration_lookup_pg.jdbc.pooling.enabled=true
bm_br_acct_accounting_entry_integration_lookup_pg.jdbc.pooling.minimumIdle=1
bm_br_acct_accounting_entry_integration_lookup_pg.jdbc.pooling.maximumPoolSize=3
bm_br_acct_accounting_entry_integration_lookup_pg.jdbc.pooling.idleTimeout=330000
bm_br_acct_accounting_entry_integration_lookup_pg.jdbc.pooling.maxLifetime=900000
bm_br_acct_accounting_entry_integration_lookup_pg.jdbc.pooling.connectionTimeout=15000

# Company-bucketed database
bm_br_acct_accounting_entry_integration_company_pg.jdbc.driverClassName=org.postgresql.Driver
bm_br_acct_accounting_entry_integration_company_pg.jdbc.dataSourceClassName=org.postgresql.ds.PGSimpleDataSource
bm_br_acct_accounting_entry_integration_company_pg.jdbc.datasource.user=bm_br_acct_accounting_entry_integration_company_user
bm_br_acct_accounting_entry_integration_company_pg.jdbc.password=password
bm_br_acct_accounting_entry_integration_company_pg.jdbc.datasource.serverName=onviopostgres.int.thomsonreuters.com
bm_br_acct_accounting_entry_integration_company_pg.jdbc.datasource.portNumber=5432
bm_br_acct_accounting_entry_integration_company_pg.jdbc.pooling.enabled=true
bm_br_acct_accounting_entry_integration_company_pg.jdbc.pooling.minimumIdle=1
bm_br_acct_accounting_entry_integration_company_pg.jdbc.pooling.maximumPoolSize=3
bm_br_acct_accounting_entry_integration_company_pg.jdbc.pooling.idleTimeout=330000
bm_br_acct_accounting_entry_integration_company_pg.jdbc.pooling.maxLifetime=900000
bm_br_acct_accounting_entry_integration_company_pg.jdbc.pooling.connectionTimeout=15000

# AMAZON S3
br.s3.region=us-east-1
br.s3.endpointOverride=http://localhost:4566
br.s3.bucket=a205451-br-services-ci-us-east-1

# XSD ACCOUNTING_ENTRY
br.accountingentry.xsd=/xml/accounting-entry.xsd

# Kafka
bm.kafka.truststore=
bm.kafka.bootstrap.servers=localhost:9092

# Kafka Topic configurations
bm.kafka.document.partitions=8
bm.kafka.document.replicas=1

aws.accessKeyId=dummyKey
aws.secretAccessKey=dummySecret

