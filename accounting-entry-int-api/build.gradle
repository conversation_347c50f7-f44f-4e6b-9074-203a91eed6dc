plugins {
    id 'org.springframework.boot' version "${springBootVersion}"
}
apply plugin: 'io.spring.dependency-management'
apply plugin: 'war'

def webContext = "internal-br-accounting-entry"

configurations {
    all {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }
}

bootRun {
    args += ["--server.port=8081", '--name="application"']
}

jar {
    enabled = false
}

dependencies {
    implementation (project(":accounting-entry-domain")) {
        exclude group: 'org.postgresql', module: 'postgresql'
        exclude group: 'org.bouncycastle', module: 'bcprov-jdk15on'
        exclude group: 'org.apache.tomcat.embed', module: 'tomcat-embed-core'
        exclude group: 'org.apache.tomcat.embed', module: 'tomcat-embed-websocket'
    }
    implementation 'org.postgresql:postgresql:42.3.9'

    // Health Check
    implementation "com.tr.bluemoon.brtap:commons-api-nrx:${brtapCommonsVersion}"
    implementation "com.tr.bluemoon.brtap:commons-kafka:${brtapCommonsVersion}"
    runtimeOnly 'org.glassfish.jaxb:jaxb-runtime:2.3.+'

    implementation 'com.tr.bluemoon.brtap:log4j-jsonevent-layout:1.0.0'


    // Spring
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation "org.springframework.boot:spring-boot-starter-amqp"
    providedRuntime ('org.springframework.boot:spring-boot-starter-tomcat'){
        exclude group: 'org.apache.tomcat.embed', module: 'tomcat-embed-core'
        exclude group: 'org.apache.tomcat.embed', module: 'tomcat-embed-websocket'
    }
    implementation 'org.apache.tomcat.embed:tomcat-embed-core:9.0.99'
    implementation 'org.apache.tomcat.embed:tomcat-embed-websocket:9.0.99'

    implementation "org.apache.kafka:kafka-clients:3.8.1"
    implementation "com.rabbitmq:amqp-client:${rabbitmqAmqpClientVersion}"

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
}

task cleanTomcat(type: Delete, dependsOn: clean) {
    def dir = System.getenv("CATALINA_HOME")
    delete "${dir}/webapps/api#${webContext}"
    delete "${dir}/resources/${webContext}"
    delete "${dir}/conf/Catalina/localhost/api#${webContext}.xml"
}

task unwar(type: Copy, dependsOn: assemble) {
    // Use the environmental variable if set or fall back to the properties file entry
    def dir = System.getenv("CATALINA_HOME")
    from zipTree(file("build/libs/${war.archiveName}"))
    into file("${dir}/webapps/api#${webContext}")
}

task copyPropertiesToTomcat(type: Copy) {
    def dir = System.getenv("CATALINA_HOME")
    if (dir != null && dir.length() > 0) {
        java.nio.file.Files.createDirectories(java.nio.file.Paths.get("${dir}/conf/Catalina/localhost"))
        def xml = new File("${dir}/conf/Catalina/localhost/api#${webContext}.xml")
        xml.withWriter { it << """<?xml version=\"1.0\" encoding=\"UTF-8\"?>
<Context>
    <Environment name=\"spring.config.location\" value=\"file:../resources/${webContext}/\" type=\"java.lang.String\"/>
</Context>""" }

        from "application.properties"
        into new File("${dir}/resources/${webContext}")
    }
}
unwar.finalizedBy copyPropertiesToTomcat

task deployCi {
    inputs.files war
    outputs.upToDateWhen { false }

    doLast {
        def parent = "" // use for testing task locally (Place your home directory)
        def vertical = "InternalBRAcctEntryIntegrationServices"
        def buildVersion = System.getenv()['BUILD_NUMBER']
        if (buildVersion.length() > 0) {
            def ciToken = "WeHaveMoreIntegrityThanKnowledge"
            def skid = new File("$parent/mnt/coderelease/$vertical/$buildVersion")
            skid.mkdirs()
            def versionText = new File("$parent/mnt/coderelease/$vertical/version_ci.txt")
            versionText.withWriter { it << "$vertical=$buildVersion" }
            def propertiesFile = new File("$skid/${vertical}.properties").createNewFile()
            logger.info("Copying jar to ${skid.getCanonicalPath()}")

            copy {
                from war
                into skid
                include '**/*.war'
                rename ".+\\.war", "${vertical}.war"
            }
            def response = "http://bluemooncistools.int.thomsonreuters.com/jenkins/job/$vertical/build?token=$ciToken".toURL().text
            println "Trigger sent to http://bluemooncistools.int.thomsonreuters.com/jenkins/job/$vertical"
        }
    }
}