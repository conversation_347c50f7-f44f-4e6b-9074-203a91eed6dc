package com.tr.bluemoon.accountingentryintegration.internal;

import com.tr.bluemoon.brtap.commons.amq.health.ResourceCheckRabbitImpl;
import com.tr.bluemoon.brtap.commons.api.health.ResourceCheckRedisImpl;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, ResourceCheckRabbitImpl.class, ResourceCheckRedisImpl.class})
@EnableWebMvc
public class InternalBRAccountingEntryIntegrationApplication {

  public static void main(String[] args) {
    SpringApplication.run(InternalBRAccountingEntryIntegrationApplication.class, args);
  }
}
