package com.tr.bluemoon.accountingentryintegration.internal;

import com.tr.bluemoon.brtap.commons.api.PropertyLoader;
import java.util.Optional;
import javax.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;

@Configuration
@ComponentScan(
    value = {
      "com.tr.bluemoon.accountingentryintegration.domain",
      "com.tr.bluemoon.brtap.commons.api.health"
    })
@PropertySource(value = "file:${applicationProperties}", ignoreResourceNotFound = true)
public class InternalBRAccountingEntryIntegrationApplicationConfig {

  @Autowired private Environment env;

  @PostConstruct
  public void postConstruct() {
    new PropertyLoader(env).load();
    final String applicationTitle =
        Optional.ofNullable(System.getProperty("instanceName"))
            .orElse("InternalBRAcctEntryIntegrationServices");
    final String applicationVersion = "1.0.0";
    final String applicationUrl = "/api/internal-br-accounting-entry";
    System.setProperty("application.title", applicationTitle);
    System.setProperty("application.version", applicationVersion);
    System.setProperty("application.url", applicationUrl);
  }
}
