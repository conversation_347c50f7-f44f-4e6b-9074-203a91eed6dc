package com.tr.bluemoon.accountingentryintegration.internal;

import java.io.File;
import java.net.URL;
import javax.servlet.ServletContext;

public class LogPropertiesListener {

  public static final String USER_DIR = System.getProperty("user.dir");
  public static final String LOG_DIRECTORY = "appLogDir";
  public static final String LOG4J_CONFIG_FILE_SYSTEM_PROPERTY = "log4jConfigurationFile";
  public static final String DEFAULT_RESOURCE_APPLICATION_LOGGING =
      "/applicationDefaultLogging.xml";

  public void configureLog(ServletContext context) {
    // Check for system properties that may get used as substitution parms in the
    // log configuration file.
    if (System.getProperty("instanceName") == null) {
      System.err.println(
          "System property not set for the instance name, local logging may not work.");
    }
    if (System.getProperty(LOG_DIRECTORY) == null) {
      String userDirLogDirectory = USER_DIR + "/logs";
      try {
        File file = new File(userDirLogDirectory);
        if (file.exists() && file.isDirectory()) {
          System.setProperty(LOG_DIRECTORY, userDirLogDirectory);
          System.out.println(
              "System property "
                  + LOG_DIRECTORY
                  + " not set, defaulting to ${user.dir}/logs, "
                  + userDirLogDirectory);
        } else {
          System.err.println(
              "System property "
                  + LOG_DIRECTORY
                  + " not set, default ${user.dir}/logs, "
                  + userDirLogDirectory
                  + " does not physically exist either, local logging may not work.");
        }
      } catch (final Exception e) {
        System.err.println(
            "System property "
                + LOG_DIRECTORY
                + " not set, default ${user.dir}/logs, "
                + userDirLogDirectory
                + " had an error determining the existence, "
                + e.toString()
                + ", local logging may not work.");
      }
    }

    String logConfigFile = System.getProperty(LOG4J_CONFIG_FILE_SYSTEM_PROPERTY);
    // If the log config file is set via system property, attempt to use it as a file on the
    // filesystem first. If it cannot
    // be found, then try to use it as a resource. If that resource does not exist, then default to
    // setting the configuration
    // to the file on the filesystem
    if (logConfigFile != null) {
      try {
        File file = new File(logConfigFile);
        if (file.exists()) {
          System.out.println("Logging initialized via " + logConfigFile);
        } else {
          URL url =
              System.class.getResource((logConfigFile.startsWith("/") ? "" : "/") + logConfigFile);
          if (url != null) {
            System.out.println("Logging initialized via " + url.toString());
          } else {
            System.out.println("Logging initialized via " + logConfigFile);
          }
        }
      } catch (final Exception e) {
        System.err.println("Logging not initialized, Exception=" + e.toString());
      }
    } else {
      // The logging config file system property was not set. Attempt to open up a known resource
      // for
      // the location of the logging file. If that does not exist, then return false as the logging
      // could not
      // be initialized
      try {
        // works for standalone java
        URL url = System.class.getResource(DEFAULT_RESOURCE_APPLICATION_LOGGING);
        if (url == null) {
          // works for tomcat
          url =
              LogPropertiesListener.class
                  .getClassLoader()
                  .getResource(DEFAULT_RESOURCE_APPLICATION_LOGGING);
        }
        if (url != null) {
          logConfigFile = url.toString();
          System.out.println("Logging initialized via " + logConfigFile);
        } else {
          System.err.println(
              "Logging not initialized, System property "
                  + LOG4J_CONFIG_FILE_SYSTEM_PROPERTY
                  + " not set "
                  + "and the default resource "
                  + DEFAULT_RESOURCE_APPLICATION_LOGGING
                  + " could not be found.");
        }
      } catch (final Exception e) {
        System.err.println("Logging not initialized, Exception=" + e.toString());
      }
    }

    if (logConfigFile != null) {
      System.setProperty("logging.config", logConfigFile);
    }
  }
}
