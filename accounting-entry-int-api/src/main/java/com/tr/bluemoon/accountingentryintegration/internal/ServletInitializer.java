package com.tr.bluemoon.accountingentryintegration.internal;

import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;

public class ServletInitializer extends SpringBootServletInitializer {

  @Override
  public void onStartup(ServletContext servletContext) throws ServletException {
    new LogPropertiesListener().configureLog(servletContext);

    super.onStartup(servletContext);
  }

  @Override
  protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
    return application.sources(InternalBRAccountingEntryIntegrationApplication.class);
  }
}
