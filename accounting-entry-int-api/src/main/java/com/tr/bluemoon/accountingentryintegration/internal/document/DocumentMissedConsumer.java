package com.tr.bluemoon.accountingentryintegration.internal.document;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.AbstractConsumer;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitConfig;
import com.tr.bluemoon.accountingentryintegration.domain.document.DocumentDeliveryProducer;
import com.tr.bluemoon.accountingentryintegration.domain.document.DocumentMissedConfig;
import com.tr.bluemoon.accountingentryintegration.domain.document.DocumentMissedEvent;
import com.tr.bluemoon.accountingentryintegration.domain.document.DocumentService;
import com.tr.bluemoon.accountingentryintegration.domain.rehydration.Rehydration;
import com.tr.bluemoon.accountingentryintegration.domain.rehydration.RehydrationService;
import com.tr.bluemoon.accountingentryintegration.domain.rehydration.RehydrationStatus;
import io.cloudevents.CloudEvent;
import java.time.LocalDateTime;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

@Component
public class DocumentMissedConsumer extends AbstractConsumer {

  private static final Logger LOGGER = LoggerFactory.getLogger(DocumentMissedConsumer.class);

  private final ObjectMapper mapper;
  private final DocumentService service;
  private final DocumentDeliveryProducer deliveryProducer;
  private final RehydrationService rehydrationService;

  @Autowired
  public DocumentMissedConsumer(
      DocumentService service, DocumentDeliveryProducer producer, ObjectMapper mapper,
      RehydrationService rehydrationService) {
    this.mapper = mapper;
    this.service = service;
    this.deliveryProducer = producer;
    this.rehydrationService = rehydrationService;
  }

  @RabbitListener(
      queues = {DocumentMissedConfig.DOCUMENT_MISSED_QUEUE},
      concurrency = "${brtap.accountingentryintegration.document-missed.concurrency:1}",
      containerFactory = "documentMissedQueueContainerFactory")
  public void consumeMessage(
      @Payload CloudEvent event,
      @Header(AmqpHeaders.RECEIVED_ROUTING_KEY) String rk,
      @Header(name = RETRY_HEADER, required = false) Map<?, ?> death) {
    try {
      final var content = event.getData();

      if (content != null) {
        final var documentMissedEvent =
            mapper.readValue(content.toBytes(), DocumentMissedEvent.class);
        final var documents = service.findAllByStartDate(documentMissedEvent.getOffset());
        final Rehydration rehydration = rehydrationService.create(documents, documentMissedEvent.getOffset());

        try {
          deliveryProducer.sendToSocket(documents, true);
          rehydration.setStatus(RehydrationStatus.SENT);
          rehydration.setSentDate(LocalDateTime.now());
        } catch (Exception e) {
          LOGGER.error("Error to send the documents to the socket.", e);
          rehydration.setStatus(RehydrationStatus.GENERIC_ERROR);
        } finally {
          rehydrationService.save(rehydration);
        }

      }
    } catch (Exception ex) {
      LOGGER.error("Error to process document missed event.", ex);
      handleRetry(event, death, RabbitConfig.ACCOUNTING_ENTRY_INTEGRATION_DEAD_LETTER_EXCHANGE, ex);
    }
  }
}
