package com.tr.bluemoon.accountingentryintegration.internal.file;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.AbstractConsumer;
import com.tr.bluemoon.accountingentryintegration.domain.amqp.RabbitConfig;
import com.tr.bluemoon.accountingentryintegration.domain.file.FileReceivedConfig;
import com.tr.bluemoon.accountingentryintegration.domain.file.FileReceivedEvent;
import com.tr.bluemoon.accountingentryintegration.domain.file.FileReceivedService;
import io.cloudevents.CloudEvent;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * File received queue consumer to validate {@link
 * com.tr.bluemoon.accountingentryintegration.domain.file.File}.
 *
 * <AUTHOR> Lanzendorf
 */
@Component
public class FileReceivedConsumer extends AbstractConsumer {

  private static final Logger LOGGER = LoggerFactory.getLogger(FileReceivedConsumer.class);

  private final ObjectMapper mapper;
  private final FileReceivedService service;

  @Autowired
  public FileReceivedConsumer(ObjectMapper mapper, FileReceivedService service) {
    this.mapper = mapper;
    this.service = service;
  }

  @RabbitListener(
      queues = {FileReceivedConfig.FILE_RECEIVED_QUEUE},
      concurrency = "${brtap.accountingentryintegration.file-received.concurrency:1}",
      containerFactory = "fileReceivedQueueContainerFactory")
  public void consumeMessage(
      @Payload CloudEvent event,
      @Header(AmqpHeaders.RECEIVED_ROUTING_KEY) String rk,
      @Header(name = RETRY_HEADER, required = false) Map<?, ?> death) {
    try {
      final var content = event.getData();

      if (content != null) {
        final var fileReceivedEvent = mapper.readValue(content.toBytes(), FileReceivedEvent.class);
        service.validate(fileReceivedEvent.getId());
      }
    } catch (Exception ex) {
      LOGGER.error("Error to process file received event.", ex);
      handleRetry(event, death, RabbitConfig.ACCOUNTING_ENTRY_INTEGRATION_DEAD_LETTER_EXCHANGE, ex);
    }
  }
}
