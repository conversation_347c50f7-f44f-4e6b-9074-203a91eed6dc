package com.tr.bluemoon.accountingentryintegration.internal.health;

import static com.tr.bluemoon.brtap.commons.api.health.Result.FAILED;
import static com.tr.bluemoon.brtap.commons.api.health.Result.OK;

import com.tr.bluemoon.accountingentryintegration.domain.database.BucketableEntityManagerProducer;
import com.tr.bluemoon.brtap.commons.api.domainawareness.DisableDomainAwareness;
import com.tr.bluemoon.brtap.commons.api.health.HealthResponse;
import com.tr.bluemoon.brtap.commons.api.health.Resource;
import com.tr.bluemoon.brtap.commons.api.health.Resources;
import com.tr.bluemoon.brtap.commons.api.security.Secured;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/v1/resourcecheck", produces = MediaType.APPLICATION_XML_VALUE)
public class HealthCheckV1Resource {

  @Autowired
  private ConnectionFactory rabbitConnectionFactory;

  @GetMapping
  @DisableDomainAwareness
  @Secured(unprotected = true)
  @Operation(description = "Check vital resources for keeping application online.")
  public HealthResponse resourceCheck() {
    final var database = new Resource("databases", isDatabasesUp() ? OK : FAILED);
    final var rabbitmq = new Resource("rabbitmq", isRabbitMQUp() ? OK : FAILED);
    return new HealthResponse(database.getResult(), new Resources(database, rabbitmq));
  }

  protected boolean isDatabasesUp() {
    return BucketableEntityManagerProducer.isDatabasesUp();
  }

  protected boolean isRabbitMQUp() {
    try (var connection = rabbitConnectionFactory.createConnection()) {
      return connection.isOpen();
    } catch (Exception e) {
      return false;
    }
  }
}
