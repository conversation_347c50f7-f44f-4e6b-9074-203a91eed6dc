<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
  <Appenders>
    <RollingFile name="LineFileAppender" fileName="${sys:appLogDir}/plain.log"
      filePattern="${sys:appLogDir}/plain.log.%i" immediateFlush="true">
      <JSONEventLayoutV1 />
      <Policies>
        <SizeBasedTriggeringPolicy size="20 MB"/>
      </Policies>
      <Filters>
        <ThresholdFilter level="info"/>
      </Filters>
    </RollingFile>
  </Appenders>
  <Loggers>
    <Root level="info">
      <AppenderRef ref="LineFileAppender"/>
    </Root>
  </Loggers>
</Configuration>