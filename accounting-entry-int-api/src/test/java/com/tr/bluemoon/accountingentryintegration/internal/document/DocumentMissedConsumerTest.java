package com.tr.bluemoon.accountingentryintegration.internal.document;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tr.bluemoon.accountingentryintegration.domain.document.Document;
import com.tr.bluemoon.accountingentryintegration.domain.document.DocumentDeliveryProducer;
import com.tr.bluemoon.accountingentryintegration.domain.document.DocumentMissedEvent;
import com.tr.bluemoon.accountingentryintegration.domain.document.DocumentService;
import com.tr.bluemoon.accountingentryintegration.domain.file.FileReceivedEvent;
import com.tr.bluemoon.accountingentryintegration.domain.rehydration.Rehydration;
import com.tr.bluemoon.accountingentryintegration.domain.rehydration.RehydrationService;
import com.tr.bluemoon.accountingentryintegration.domain.rehydration.RehydrationStatus;
import io.cloudevents.CloudEvent;
import io.cloudevents.CloudEventData;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.amqp.AmqpRejectAndDontRequeueException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

class DocumentMissedConsumerTest {

  @Mock ObjectMapper mapper;

  @Mock CloudEvent cloudEvent;

  @Mock CloudEventData cloudEventData;

  @Mock DocumentService documentService;

  @Mock RehydrationService rehydrationService;

  @Mock DocumentDeliveryProducer producer;

  @Mock RabbitTemplate rabbitTemplate;

  @InjectMocks DocumentMissedConsumer consumer;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void shouldReceiveFindByOffsetAndSendToSocket() throws IOException {
    // Arrange
    final var event = new DocumentMissedEvent();
    final var documents = List.of(new Document());
    final var offset = LocalDateTime.of(2022, 5, 10, 13, 20);
    event.setOffset(offset);
    final var rehydration = new Rehydration();

    Mockito.when(cloudEvent.getData()).thenReturn(cloudEventData);
    Mockito.when(mapper.readValue(cloudEventData.toBytes(), DocumentMissedEvent.class))
        .thenReturn(event);
    Mockito.when(documentService.findAllByStartDate(offset)).thenReturn(documents);
    Mockito.when(rehydrationService.create(documents, offset)).thenReturn(rehydration);

    rehydration.setStatus(RehydrationStatus.SENT);
    rehydration.setSentDate(LocalDateTime.now());
    Mockito.when(rehydrationService.save(Mockito.any(Rehydration.class))).thenReturn(rehydration);

    // Act
    consumer.consumeMessage(cloudEvent, "routing.key", Map.of());

    // Assert
    Mockito.verify(documentService).findAllByStartDate(offset);
    Mockito.verify(rehydrationService).create(Mockito.any(), Mockito.any());
    Mockito.verify(rehydrationService).save(Mockito.any());
    Mockito.verify(producer).sendToSocket(documents, true);
  }

  @Test
  void shouldReceiveAndDoNothingWithNullEvent() {
    // Arrange
    final var event = new DocumentMissedEvent();

    Mockito.when(cloudEvent.getData()).thenReturn(null);

    // Act
    consumer.consumeMessage(cloudEvent, "routing.key", Map.of());

    // Assert
    Mockito.verify(documentService, Mockito.never()).findAllByStartDate(Mockito.any());
    Mockito.verify(rehydrationService, Mockito.never()).create(Mockito.any(), Mockito.any());
    Mockito.verify(rehydrationService, Mockito.never()).save(Mockito.any());
    Mockito.verify(producer, Mockito.never()).sendToSocket(Mockito.any(), Mockito.anyBoolean());
  }

  @Test
  void shouldThrowAmqpRejectAndDontRequeueException() {
    // Arrange
    final var event = new FileReceivedEvent();
    event.setId(UUID.randomUUID());

    Mockito.doThrow(NullPointerException.class).when(cloudEvent).getData();

    // Act
    Assertions.assertThrows(
        AmqpRejectAndDontRequeueException.class,
        () -> consumer.consumeMessage(cloudEvent, "routing.key", Map.of()));

    // Assert
    Mockito.verify(documentService, Mockito.never()).findAllByStartDate(Mockito.any());
    Mockito.verify(producer, Mockito.never()).sendToSocket(Mockito.any(), Mockito.anyBoolean());
  }
}
