package com.tr.bluemoon.accountingentryintegration.internal.file;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tr.bluemoon.accountingentryintegration.domain.file.FileReceivedEvent;
import com.tr.bluemoon.accountingentryintegration.domain.file.FileReceivedService;
import io.cloudevents.CloudEvent;
import io.cloudevents.CloudEventData;
import java.io.IOException;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.amqp.AmqpRejectAndDontRequeueException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

class FileReceivedConsumerTest {

  @Mock ObjectMapper mapper;

  @Mock CloudEvent cloudEvent;

  @Mock CloudEventData cloudEventData;

  @Mock FileReceivedService fileReceivedService;

  @Mock RabbitTemplate rabbitTemplate;

  @InjectMocks FileReceivedConsumer consumer;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void shouldReceiveFileAndValidate() throws IOException {
    // Arrange
    final var event = new FileReceivedEvent();
    event.setId(UUID.randomUUID());

    Mockito.when(cloudEvent.getData()).thenReturn(cloudEventData);

    Mockito.when(mapper.readValue(cloudEventData.toBytes(), FileReceivedEvent.class))
        .thenReturn(event);

    // Act
    consumer.consumeMessage(cloudEvent, "routing.key", Map.of());

    // Assert
    Mockito.verify(fileReceivedService).validate(event.getId());
  }

  @Test
  void shouldReceiveFileAndNotValidateWithNullEvent() {
    // Arrange
    final var event = new FileReceivedEvent();
    event.setId(UUID.randomUUID());

    Mockito.when(cloudEvent.getData()).thenReturn(null);

    // Act
    consumer.consumeMessage(cloudEvent, "routing.key", Map.of());

    // Assert
    Mockito.verify(fileReceivedService, Mockito.never()).validate(event.getId());
  }

  @Test
  void shouldThrowAmqpRejectAndDontRequeueException() {
    // Arrange
    final var event = new FileReceivedEvent();
    event.setId(UUID.randomUUID());

    Mockito.doThrow(NullPointerException.class).when(cloudEvent).getData();

    // Act
    Assertions.assertThrows(
        AmqpRejectAndDontRequeueException.class,
        () -> consumer.consumeMessage(cloudEvent, "routing.key", Map.of()));

    // Assert
    Mockito.verify(fileReceivedService, Mockito.never()).validate(event.getId());
  }
}
