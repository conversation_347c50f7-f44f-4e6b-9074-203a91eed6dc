package com.tr.bluemoon.accountingentryintegration.internal.health;

import com.tr.bluemoon.brtap.commons.api.health.Result;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class HealthCheckV1ResourceTest {

  @Test
  void shouldResourcesBeOkay() {
    final var healthCheck = Mockito.spy(new HealthCheckV1Resource());
    Mockito.doReturn(true).when(healthCheck).isDatabasesUp();
    Mockito.doReturn(true).when(healthCheck).isRabbitMQUp();

    final var response = healthCheck.resourceCheck();
    Assertions.assertEquals(Result.OK, response.getResult());
  }
}
