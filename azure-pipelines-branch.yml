name: $(BranchBuildNumber)$(Rev:.r)

trigger: none

pr: none

variables:
  - group: BlueMoon release number

stages:
  - stage: build
    variables:
      - group: aafm-platform-gradle-build
    jobs:
      - job: 'Build_and_deploy'
        pool:
          name: 'common-back-end'

        container:
          image: tr1-docker.jfrog.io/aafm/br/onvio/pipelines-java-docker:jdk11
          options: --privileged

        steps:
          - checkout: self
            persistCredentials: true
            clean: true

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                echo 'Mounting landing skid...'
                mkdir -p /mnt/coderelease
                mount -t cifs -o user=root,pass= $(remote_landing_skid_unc_name) /mnt/coderelease
                echo 'Landing skid mounted successfully.'
            displayName: 'Mounting landing skid'

          - task: Gradle@2
            env:
              BUILD_NUMBER: $(Build.BuildNumber)
              BUILD_URL: $(Build.BuildUri)
            inputs:
              workingDirectory: ''
              gradleWrapperFile: 'gradlew'
              gradleOptions: '-Xmx3072m'
              javaHomeOption: 'JDKVersion'
              publishJUnitResults: true
              testResultsFiles: '**/TEST-*.xml'
              tasks: 'bootJar deployCi -Ptr1_user=$(tr1_user) -Ptr1_password=$(tr1_password)'
            condition: succeeded()
            displayName: 'Build snapshot, copy to landing skid and deploy version $(Build.BuildNumber) to CI'

          - task: PublishCodeCoverageResults@1
            inputs:
              codeCoverageTool: 'JaCoCo'
              summaryFileLocation: './build/reports/jacoco.xml'

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                git config --global user.email "<EMAIL>";
                git config --global user.name "TaxProf Bot";
                git tag -a "$(Build.BuildNumber)" -m "Release $(Build.BuildNumber)" $(Build.SourceVersion);
                git push origin "$(Build.BuildNumber)";
            condition: succeeded()
            displayName: 'Tag source code'

          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                echo 'Checking if /mnt/coderelease exists...'
                if [ -d "/mnt/coderelease" ]; then
                  echo 'Unmounting landing skid...'
                  umount /mnt/coderelease
                  echo 'Landing skid unmounted successfully.'
                else
                  echo 'Directory /mnt/coderelease does not exist. Skipping unmount.'
                fi
            condition: always()
            displayName: 'Unmount landing skid'
