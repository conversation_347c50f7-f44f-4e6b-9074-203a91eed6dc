name: $(BlueMoonRelease)$(Rev:.r)-gated

trigger: none
pr:
  branches:
    include: []  # No branches specified means all branches are included

variables:
  - group: BlueMoon release number
  - group: aafm-platform-gradle-build

pool: common-back-end

container:
  image: tr1-docker.jfrog.io/aafm/br/onvio/pipelines-java-docker:jdk11
  options: --privileged

steps:
  - task: Gradle@2
    inputs:
      workingDirectory: ''
      gradleWrapperFile: 'gradlew'
      gradleOptions: '-Xmx3072m'
      javaHomeOption: 'JDKVersion'
      tasks: 'clean build javadoc test -Ptr1_user=$(tr1_user) -Ptr1_password=$(tr1_password)'
    displayName: 'Build, javadoc and unit test validation'

  - script: |
      echo "Downloading Maven..."
      curl -fsSL https://archive.apache.org/dist/maven/maven-3/3.8.8/binaries/apache-maven-3.8.8-bin.tar.gz -o maven.tar.gz
      echo "Extracting Maven..."
      tar xzvf maven.tar.gz -C /opt
      echo "Adding Maven to PATH..."
      echo "##vso[task.prependpath]/opt/apache-maven-3.8.8/bin"
    displayName: 'Install Maven'

  - task: SnykSecurityScan@1
    inputs:
      serviceConnectionEndpoint: 'Snyk_205272OnvioBR_Custodian'
      testType: 'app'
      severityThreshold: 'high'
      monitorWhen: 'always'
      failOnIssues: false
      organization: '205272_onv_br_mod'
      additionalArguments: '--all-projects --scan-all-unmanaged --artifactory-url=$(artifactory_contextUrl) --artifactory-username=$(tr1_user) --artifactory-password=$(tr1_password)'

  - task: SnykSecurityScan@1
    inputs:
      serviceConnectionEndpoint: 'Snyk_205272OnvioBR_Custodian'
      testType: 'code'
      codeSeverityThreshold: 'high'
      failOnIssues: false
      organization: '205272_onv_br_mod'
      additionalArguments: '--all-projects --scan-all-unmanaged --artifactory-url=$(artifactory_contextUrl) --artifactory-username=$(tr1_user) --artifactory-password=$(tr1_password)'
