buildscript {
    repositories {
        maven {
            url "${artifactory_contextUrl}/libs-release"
            credentials {
                username = "${tr1_user}"
                password = "${tr1_password}"
            }
        }
    }
}

plugins {
    id 'org.sonarqube' version '2.8'
    id 'com.jfrog.artifactory' version '4.28.2'
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
//    id 'org.springframework.boot' version "${springBootVersion}"
}
// Update spring managed dependency snakeyaml to v2.0
ext['snakeyaml.version']='2.0'
// Update spring boot version
ext['spring-framework.version']='5.3.33'
//Update the jackson version to fix snyk vulnerability
ext['jackson.version'] = '2.15.0'
// Update netty-handler version
ext['netty-handler.version'] = '4.1.118.Final'

// exclude these files from the coverage report
def excludePattern = [
        '**/BRAccountingEntryApplication.*',
        '**/CustomReader.*',
        '**/InternalBRAccountingEntryIntegrationApplication.*',
        '**/ServletInitializer.*',
        '**/LogPropertiesListener.*',
        '**/BucketableEntityManagerProducer.*',
        '**/BucketableEntityManagerFactory.*',
        '**/InvoicingBucketableEntityManagerProducer.*',
        '**/SwaggerV1Resource.*',
        '**/*Config.*',
        '**/*Repository.*',
        '**/*Dao.*'
]

allprojects {
    apply plugin: 'java'
    apply plugin: 'com.jfrog.artifactory'
    apply plugin: 'jacoco'

    group = "com.tr.bluemoon"

    sourceCompatibility = '11'
    targetCompatibility = '11'

    configurations {
        configureEach {
            resolutionStrategy {
                force 'io.netty:netty-handler:4.1.118.Final'
            }
        }
    }

    repositories {
        maven {
            url "${artifactory_contextUrl}/${artifactory_resolve_repoKey_release}"
            credentials {
                username = "${tr1_user}"
                password = "${tr1_password}"
            }
        }
        maven {
            url "${artifactory_contextUrl}/${artifactory_resolve_repoKey_snapshot}"
            credentials {
                username = "${tr1_user}"
                password = "${tr1_password}"
            }
        }
    }

    jacoco {
        toolVersion = "0.8.6"
        reportsDirectory = file("$buildDir/jacoco")
    }
}

subprojects {
    apply plugin: 'checkstyle'

    dependencies {
        testImplementation "org.junit.jupiter:junit-jupiter-api:${junitVersion}"
        testImplementation "org.junit.jupiter:junit-jupiter-params:${junitVersion}"
        testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:${junitVersion}"
        testImplementation "org.mockito:mockito-core:${mockitoVersion}"
        testImplementation "net.bytebuddy:byte-buddy:1.12.10"
        implementation("io.netty:netty-handler:4.1.121.Final") {
            exclude group: 'io.netty', module: 'netty-common'
        }
        implementation "io.netty:netty-common:4.1.121.Final"

        // Lombok
        compileOnly "org.projectlombok:lombok:${lombokVersion}"
        annotationProcessor "org.projectlombok:lombok:${lombokVersion}"

        testCompileOnly "org.projectlombok:lombok:${lombokVersion}"
        testAnnotationProcessor "org.projectlombok:lombok:${lombokVersion}"
    }

    dependencyManagement {
        imports {
            mavenBom "com.fasterxml.jackson:jackson-bom:2.15.0"
        }
    }

    checkstyle {
        toolVersion '8.32'
        ignoreFailures = false
        maxErrors = 0
        maxWarnings = 0
    }

    // Test config
    test {
        useJUnitPlatform()

        finalizedBy jacocoTestReport
    }

    jacocoTestCoverageVerification {
        getSourceDirectories().from(files(sourceSets.getByName("main").allSource.srcDirs))
        afterEvaluate {
            getClassDirectories().setFrom(files(classDirectories.files.collect {
                fileTree(dir: it, exclude: excludePattern)
            }))
        }

        violationRules {
            rule {
                limit {
                    minimum = 0.80
                }
            }
        }
    }
    check.dependsOn jacocoTestCoverageVerification

    jacocoTestReport {
        afterEvaluate {
            classDirectories.setFrom(files(classDirectories.files.collect {
                fileTree(dir: it, exclude: excludePattern)
            }))
        }
    }

    // Ensure that the HTML reports of unit and integration tests are written to different directories.
    tasks.withType(Test) {
        reports.html.setDestination(file("${reporting.baseDir}/${name}"))

        testLogging {
            // Show that tests are run in the command-line output
            events 'passed'
        }
    }

    configurations {
        configureEach {
            exclude group: 'com.tr.bluemoon', module: 'environments-lib'
            resolutionStrategy {
                force 'io.netty:netty-handler:4.1.118.Final'
            }
        }
    }
}

task coverageReport(type: JacocoReport) {
    dependsOn = subprojects.test
    additionalSourceDirs.setFrom files(subprojects.sourceSets.main.allSource.srcDirs)
    sourceDirectories.setFrom files(subprojects.sourceSets.main.allSource.srcDirs)
    classDirectories.setFrom files(subprojects.sourceSets.main.output)
    executionData.setFrom project.fileTree(dir: '.', include: '**/build/jacoco/test.exec')
    afterEvaluate {
        getClassDirectories().setFrom(files(classDirectories.files.collect {
            fileTree(dir: it, exclude: excludePattern)
        }))
    }
    reports {
        xml.enabled true
        xml.destination file("${buildDir}/reports/jacoco.xml")
        html.enabled true
        html.destination file("${buildDir}/reports/html")
        csv.enabled false
    }
}

project.tasks["sonarqube"].dependsOn coverageReport

sonarqube {
    properties {
        property 'sonar.host.url', "${sonar_hostUrl}"
        property 'sonar.projectName', vertical
        property 'sonar.projectKey', "com.thomsonreuters:${vertical}"
        property 'sonar.projectVersion', System.getenv()['BUILD_NUMBER']
        property 'sonar.core.codeCoveragePlugin', 'jacoco'
        property 'sonar.coverage.jacoco.xmlReportPaths', "${buildDir}/reports/jacoco.xml"
        property 'sonar.coverage.exclusions', excludePattern
    }
}
