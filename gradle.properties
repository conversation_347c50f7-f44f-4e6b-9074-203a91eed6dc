vertical=BRAccountingEntryServices
artifactory_contextUrl=https://tr1.jfrog.io/tr1
artifactory_publish_repoKey_release=libs-release-local
artifactory_publish_repoKey_snapshot=libs-snapshot-local
artifactory_resolve_repoKey_release=libs-release
artifactory_resolve_repoKey_snapshot=libs-snapshot

# Username and password properties should remain blank
tr1_user=
tr1_password=

sonar_hostUrl=https://bluemoon-dashboard.int.thomsonreuters.com/sonar

junitVersion=5.8.2
mockitoVersion=4.5.1
lombokVersion=1.18.24
jerseyVersion=2.41
deltaSpikeVersion=1.9.4
eclipseLinkVersion=2.7.3
bracctCommonsVersion=5.0.0-rc.2
bracctCommonsRestVersion=5.0.0-rc.1
bracctCommonsJpaVersion=4.0.0-rc.1

springBootVersion=2.7.18
springVersion=5.3.33
brtapCommonsVersion=2.1.0-rc.10
platformCommonsVersion=1.0.0
rabbitmqAmqpClientVersion=5.20.0
postgresqlVersion=42.6.2