pluginManagement {
    repositories {
        maven {
            url "${settings.ext.find('artifactory_contextUrl') ?: System.getProperty('artifactory_contextUrl') ?: 'https://tr1.jfrog.io/tr1'}/libs-release"
            credentials {
                username = settings.ext.find('tr1_user') ?: System.getProperty('tr1_user') ?: ''
                password = settings.ext.find('tr1_password') ?: System.getProperty('tr1_password') ?: ''
            }
        }
        maven {
            url "${settings.ext.find('artifactory_contextUrl') ?: System.getProperty('artifactory_contextUrl') ?: 'https://tr1.jfrog.io/tr1'}/libs-snapshot"
            credentials {
                username = settings.ext.find('tr1_user') ?: System.getProperty('tr1_user') ?: ''
                password = settings.ext.find('tr1_password') ?: System.getProperty('tr1_password') ?: ''
            }
        }
    }
}

rootProject.name = 'brtap-accounting-entry'

include 'accounting-entry-api'
include 'accounting-entry-int-api'
include 'accounting-entry-domain'
include 'accounting-entry-database'
